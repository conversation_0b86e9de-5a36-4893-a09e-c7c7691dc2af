"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const permissions_service_1 = require("../modules/permissions/permissions.service");
const profile_permissions_service_1 = require("../modules/profile-permissions/profile-permissions.service");
const users_service_1 = require("../modules/users/users.service");
const profiles_service_1 = require("../modules/profiles/profiles.service");
let PermissionsGuard = class PermissionsGuard {
    constructor(reflector, permissionsService, profilePermissionsService, usersService, profilesService) {
        this.reflector = reflector;
        this.permissionsService = permissionsService;
        this.profilePermissionsService = profilePermissionsService;
        this.usersService = usersService;
        this.profilesService = profilesService;
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const method = request.method.toLowerCase();
        let resource = this.reflector.get('resource', context.getHandler());
        if (!resource) {
            resource = this.reflector.get('resource', context.getClass());
        }
        if (!resource) {
            console.log('Resource not defined, allowing access');
            return true;
        }
        const operation = this.mapHttpMethodToOperation(method);
        const permissionKey = `${resource.toUpperCase()}_${operation.toUpperCase()}`;
        const userId = request.user?.id;
        if (!userId) {
            throw new common_1.ForbiddenException('Access denied: user not authenticated');
        }
        const user = await this.usersService.findOne(userId);
        if (!user) {
            throw new common_1.ForbiddenException('Access denied: user not found');
        }
        const profile = await this.profilesService.findOne(user.profileId);
        if (!profile) {
            throw new common_1.ForbiddenException('Access denied: user profile not found');
        }
        request.user = {
            id: user.id,
            email: user.email,
            agencyId: user.agencyId,
            cooperativeId: user.cooperativeId,
            centralId: user.centralId,
            federationId: user.federationId,
            profile: { id: user.profileId, hierarchy: profile.hierarchy, key: profile.key },
        };
        const skipPermissionCheck = this.reflector.get('noPermission', context.getHandler());
        if (skipPermissionCheck) {
            return true;
        }
        const permissions = await this.permissionsService.findByKey(permissionKey);
        if (!permissions || permissions.length === 0) {
            throw new common_1.ForbiddenException(`Access denied: permission '${permissionKey}' not found`);
        }
        const hasPermission = await (async () => {
            for (const permission of permissions) {
                const profilePermission = await this.profilePermissionsService.findByProfileIdAndPermissionId(user.profileId, permission.id);
                if (profilePermission) {
                    return true;
                }
            }
            return false;
        })();
        if (!hasPermission) {
            throw new common_1.ForbiddenException('Access denied: insufficient permissions');
        }
        return true;
    }
    mapHttpMethodToOperation(method) {
        const map = {
            get: 'view',
            post: 'create',
            patch: 'update',
            put: 'update',
            delete: 'delete',
        };
        return map[method] || 'unknown';
    }
};
exports.PermissionsGuard = PermissionsGuard;
exports.PermissionsGuard = PermissionsGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        permissions_service_1.PermissionsService,
        profile_permissions_service_1.ProfilePermissionsService,
        users_service_1.UsersService,
        profiles_service_1.ProfilesService])
], PermissionsGuard);
//# sourceMappingURL=permissions.guard.js.map