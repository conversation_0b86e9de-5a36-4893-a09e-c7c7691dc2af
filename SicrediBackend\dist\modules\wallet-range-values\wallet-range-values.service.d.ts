import { CreateWalletRangeValueDto } from './dto/create-wallet-range-value.dto';
import { UpdateWalletRangeValueDto } from './dto/update-wallet-range-value.dto';
import { WalletRangeValue } from './entities/wallet-range-value.entity';
import { Repository } from 'typeorm';
import { SegmentsService } from '../segments/segments.service';
import { AgenciesService } from '../agencies/agencies.service';
import { ToUpdateWalletRangeDto } from './dto/to-update-wallet-range.dto';
import { WalletRangeValuesDto } from './dto/wallet-range-values.dto';
import { Wallet } from '../wallets/entities/wallet.entity';
import { Segment } from '../segments/entities/segment.entity';
import { Agency } from '../agencies/entities/agency.entity';
export declare class WalletRangeValuesService {
    private readonly walletRangeValueRepository;
    private readonly segmentService;
    private readonly segmentRepository;
    private readonly agencyRepository;
    private readonly walletsRepository;
    private readonly agenciesService;
    constructor(walletRangeValueRepository: Repository<WalletRangeValue>, segmentService: SegmentsService, segmentRepository: Repository<Segment>, agencyRepository: Repository<Agency>, walletsRepository: Repository<Wallet>, agenciesService: AgenciesService);
    create(createWalletRangeValueDto: CreateWalletRangeValueDto): Promise<{
        id: number;
        name: string;
    }>;
    findAll(): Promise<CreateWalletRangeValueDto[]>;
    findOne(identifier: string | number): Promise<WalletRangeValue>;
    update(id: number, updateWalletDto: UpdateWalletRangeValueDto): Promise<UpdateWalletRangeValueDto>;
    remove(id: number): Promise<void>;
    findBySegment(id: number): Promise<CreateWalletRangeValueDto[]>;
    createWalletRangeValuesDefaultToAgencies(idAgency: number): Promise<void>;
    findByAgency(id: number): Promise<CreateWalletRangeValueDto[]>;
    findByCooperative(id: number): Promise<CreateWalletRangeValueDto[]>;
    getNumbersWalletRangeValuesByAgencyId(agencyId: number, segmentId: number): Promise<string[]>;
    toUpdateWalletRange(id: number): Promise<ToUpdateWalletRangeDto>;
    createWalletRangeFromBulk(walletRangeValueDto: WalletRangeValuesDto[]): Promise<any>;
    deleteWalletRangesFromBulk(walletRangeCodes: string[]): Promise<any>;
}
