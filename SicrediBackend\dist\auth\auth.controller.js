"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const auth_dto_1 = require("./dto/auth.dto");
const signIn_dto_1 = require("./dto/signIn.dto");
const refreshToken_dto_1 = require("./dto/refreshToken.dto");
const user_dto_1 = require("../modules/users/dto/user.dto");
const public_decorator_1 = require("../common/decorators/public.decorator");
const keycloak_role_mapper_1 = require("../utils/keycloak-role-mapper");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async signIn(signInDto) {
        return await this.authService.signIn(signInDto.login, signInDto.password);
    }
    async refreshToken(refreshTokenDto) {
        return await this.authService.refreshAccessToken(refreshTokenDto.refreshToken);
    }
    async logout(body) {
        const success = await this.authService.logout(body.refreshToken);
        if (success) {
            return { message: 'Logout realizado com sucesso' };
        }
        else {
            return { message: 'Logout realizado localmente' };
        }
    }
    async findByToken(authorization) {
        if (!authorization) {
            throw new Error('Authorization header missing');
        }
        const token = authorization.replace('Bearer ', '');
        return this.authService.findByToken(token);
    }
    async testRoleMapping(body) {
        const mappedProfile = keycloak_role_mapper_1.KeycloakRoleMapper.mapRolesToProfile(body.roles);
        const availableRoles = keycloak_role_mapper_1.KeycloakRoleMapper.getAvailableRoles();
        const availableProfiles = keycloak_role_mapper_1.KeycloakRoleMapper.getAvailableProfiles();
        return {
            roles: body.roles,
            mappedProfile,
            availableRoles,
            availableProfiles,
        };
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Login de usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Login bem-sucedido.',
        type: auth_dto_1.AuthResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Parâmetros inválidos.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('/login'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [signIn_dto_1.SignInDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signIn", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualiza o token de acesso' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Token atualizado com sucesso.',
        schema: { example: { access_token: 'new-access-token', refresh_token: 'new-refresh-token' } },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token de atualização inválido.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('/refresh'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [refreshToken_dto_1.RefreshTokenDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refreshToken", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Logout do usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Logout realizado com sucesso.',
        schema: { example: { message: 'Logout realizado com sucesso' } },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Erro no logout.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('/logout'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar usuário pelo Token' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário encontrado com sucesso.',
        type: user_dto_1.UserDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado.' }),
    (0, common_1.Get)('me'),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "findByToken", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Testar mapeamento de roles do Keycloak' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Mapeamento testado com sucesso.',
        schema: {
            type: 'object',
            properties: {
                roles: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['admin_sicredi', 'pa_pls_admin'],
                },
                mappedProfile: {
                    type: 'string',
                    example: 'ADMIN',
                },
                availableRoles: {
                    type: 'array',
                    items: { type: 'string' },
                },
                availableProfiles: {
                    type: 'array',
                    items: { type: 'string' },
                },
            },
        },
    }),
    (0, common_1.Post)('test-role-mapping'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "testRoleMapping", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('api/v1/auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map