"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const users_service_1 = require("./users.service");
const user_dto_1 = require("./dto/user.dto");
const createUserResponse_dto_1 = require("./dto/createUserResponse.dto");
const createUser_dto_1 = require("./dto/createUser.dto");
const updateUser_dto_1 = require("./dto/updateUser.dto");
const searchUser_dto_1 = require("./dto/searchUser.dto");
const paginatedUser_dto_1 = require("./dto/paginatedUser.dto");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
const user_decorator_1 = require("../../common/decorators/user.decorator");
let UsersController = class UsersController {
    constructor(usersService) {
        this.usersService = usersService;
    }
    searchByName(searchUser) {
        return this.usersService.searchByName(searchUser);
    }
    findById(id) {
        return this.usersService.findById(+id);
    }
    async create(user, request) {
        return await this.usersService.create(user, request.accessTokenJWT);
    }
    update(id, user, request) {
        return this.usersService.update(+id, user, request.accessTokenJWT);
    }
    remove(id, request) {
        return this.usersService.remove(+id, request.accessTokenJWT);
    }
    findAll() {
        return this.usersService.findAll();
    }
    findAllByAgency(id, filter) {
        return this.usersService.findAllByAgency(+id, filter);
    }
    async findPaginatedUsers(user, paginationParams) {
        return this.usersService.findPaginatedUsers(user.profile.hierarchy, paginationParams);
    }
    createBulk(userDto, request) {
        return this.usersService.createUserFromBulk(userDto, request.accessTokenJWT);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar usuários pelo nome' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuários encontrados com sucesso.',
        type: [user_dto_1.UserDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuários não encontrados.' }),
    (0, swagger_1.ApiQuery)({
        name: 'name',
        required: false,
        type: String,
        description: 'Nome do usuário para busca. (opcional)',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Get)('search-by-name'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [searchUser_dto_1.SearchUserDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "searchByName", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar usuário pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário encontrado com sucesso.',
        type: user_dto_1.UserDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado.' }),
    (0, common_1.Get)('find-by-id/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findById", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Usuário criado com sucesso.',
        type: createUserResponse_dto_1.CreateUserResponse,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createUser_dto_1.CreateUserDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário atualizado com sucesso.',
        type: user_dto_1.UserDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateUser_dto_1.UpdateUserDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover usuário' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Usuário removido com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Usuários' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Usuários encontrada com sucesso.',
        type: user_dto_1.UserDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Usuário encontrado.' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Usuários gerentes de carteira e assistentes filtrando por id de agência e nome.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Usuários encontrada com sucesso.',
        type: user_dto_1.UserDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Usuário encontrado.' }),
    (0, common_1.Get)('user-by-agency/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('filter')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findAllByAgency", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/users'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Usuários encontrada com sucesso.',
        schema: {
            example: {
                items: [
                    {
                        id: 1,
                        phone: '12312312312',
                        name: 'Admin User',
                        email: '<EMAIL>',
                        lastName: null,
                        profileId: 1,
                        profileName: 'Administrador',
                    },
                ],
                totalItems: 100,
                totalPages: 10,
                currentPage: 1,
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Usuário encontrado.' }),
    (0, common_1.Get)('paginated-users'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, paginatedUser_dto_1.PaginatedUsersDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findPaginatedUsers", null);
__decorate([
    (0, swagger_1.ApiExcludeEndpoint)(),
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar usuários em massa',
        description: 'Este endpoint permite criar novos usuários ou atualizar usuários existentes em massa. Se um usuário já existir, seus dados serão atualizados com as informações enviadas.',
    }),
    (0, swagger_1.ApiBody)({
        type: [user_dto_1.UserDto],
        description: 'Array de objetos contendo os dados dos usuários para criação ou atualização em massa.',
        examples: {
            exemplo: {
                summary: 'Exemplo de criação e atualização de usuários em massa',
                value: [
                    {
                        id: 1,
                        active: true,
                        name: 'John Doe',
                        email: '<EMAIL>',
                        profileId: 2,
                        profileKey: 'USER',
                        phone: '99999999999',
                        permissions: ['CREATE_TABLE', 'UPDATE_RECORD'],
                        profileName: 'Usuário',
                        hierarchy: 1,
                        agency_code: 'AG123',
                        central_name: 'Central 1',
                        cooperative_name: 'Cooperative A',
                        federation_name: 'Federation X',
                        cnpj: '03535566000106',
                        cpf: '02366478062',
                        serviceUnitNumber: 'SU123',
                        birthDate: '2000-01-01',
                        lastName: 'Doe',
                        password: 'password123',
                        idKeyCloak: 'idKeyCloak',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Usuários criados ou atualizados com sucesso.',
        schema: {
            example: {
                processedUsers: [
                    {
                        id: 2,
                        phone: '1234567890',
                        name: 'User 1',
                        lastName: 'Last Name',
                        email: '<EMAIL>',
                        profileId: 2,
                        profileName: 'User',
                        updated: true,
                    },
                ],
                errors: [],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição mal formatada ou campos obrigatórios faltando.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns usuários foram processados com sucesso, mas outros tiveram erros.',
        schema: {
            example: {
                processedUsers: [
                    {
                        id: 3,
                        phone: '987654321',
                        name: 'User 2',
                        lastName: 'Last Name',
                        email: '<EMAIL>',
                        profileId: 5,
                        profileName: 'Admin',
                        updated: false,
                    },
                ],
                errors: [
                    {
                        user: {
                            name: 'Invalid User',
                            email: '<EMAIL>',
                        },
                        status: 'error',
                        message: 'User already exists in Keycloak',
                    },
                ],
            },
        },
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "createBulk", null);
exports.UsersController = UsersController = __decorate([
    (0, permission_decorator_1.Permission)('users'),
    (0, common_1.Controller)('/api/v1/users'),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map