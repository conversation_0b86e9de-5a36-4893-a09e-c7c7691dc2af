import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthResponseDto } from './dto/auth.dto';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { jwtDecode } from 'jwt-decode';
import { UsersService } from './../modules/users/users.service';
import { DataSource } from 'typeorm';
import { Attendance } from 'src/modules/attendances/entities/attendance.entity';
import { endOfDay, startOfDay } from 'date-fns';

@Injectable()
export class AuthService {
  // KEYCLOAK SICREDI EXTERNO
  private readonly keycloakUrl: string;
  private readonly keycloakRealm: string;
  private readonly keycloakClientId: string;
  private readonly keycloakScope: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly http: HttpService,
    private readonly usersService: UsersService,
    private readonly dataSource: DataSource,
  ) {
    // CONFIGURAÇÕES DO KEYCLOAK EXTERNO DA SICREDI
    this.keycloakUrl = this.configService.get<string>('SICREDI_KEYCLOAK_URL') || 'https://auth-em.dev.sicredi.io';
    this.keycloakRealm = this.configService.get<string>('SICREDI_KEYCLOAK_REALM') || 'employee';
    this.keycloakClientId = this.configService.get<string>('SICREDI_KEYCLOAK_CLIENT_ID') || 'sicredi-public';
    this.keycloakScope = this.configService.get<string>('SICREDI_KEYCLOAK_SCOPE') || 'openid application:portal_aplicacoes';
  }

  async signIn(login: string, password: string): Promise<AuthResponseDto> {
    try {
      // CHAMADA PARA O KEYCLOAK EXTERNO DA SICREDI
      const { data, status } = await firstValueFrom(
        this.http.post(
          `${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/token`,
          new URLSearchParams({
            grant_type: 'password',
            client_id: this.keycloakClientId,
            username: login,
            password: password,
            scope: this.keycloakScope,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        ),
      );

      if (status !== 200) {
        throw new UnauthorizedException('Invalid credentials');
      }

      if (!data.access_token) {
        throw new UnauthorizedException('Invalid Access Token');
      }
      
      // DECODIFICAR TOKEN PARA EXTRAIR INFORMAÇÕES DO USUÁRIO
      const decodedToken = jwtDecode(data.access_token) as any;
      if (!decodedToken?.sub) {
        throw new UnauthorizedException('Invalid Access Token');
      }

      // BUSCAR INFORMAÇÕES DO USUÁRIO NO KEYCLOAK
      const userInfo = await this.getUserInfo(data.access_token);

      try {
              // BUSCAR OU CRIAR USUÁRIO NO BANCO LOCAL
      let user = await this.usersService.findByKeycloakUsername(decodedToken.preferred_username);
      
      // EXTRAIR ROLES DO TOKEN KEYCLOAK CORRETAMENTE
      const keycloakRoles = this.extractRolesFromToken(decodedToken);

      // LOG PARA DEBUG - ROLES DO KEYCLOAK
      console.log('=== DEBUG KEYCLOAK ROLES ===');
      console.log('Username:', decodedToken.preferred_username);
      console.log('Roles extraídas:', keycloakRoles);
      console.log('Token roles direto:', decodedToken.roles || []);
      console.log('Realm Access Roles:', decodedToken.realm_access?.roles || []);
      console.log('Resource Access Roles:', decodedToken.resource_access || {});
      console.log('===========================');

        if (!user) {
          // CRIAR USUÁRIO AUTOMATICAMENTE SE NÃO EXISTIR
          user = await this.usersService.createFromKeycloakUser({
            keycloakId: decodedToken.sub,
            username: decodedToken.preferred_username,
            email: userInfo.email,
            name: userInfo.given_name || userInfo.name,
            lastName: userInfo.family_name || '',
            roles: keycloakRoles,
            sub: decodedToken.sub, // PASSAR O CAMPO SUB DO KEYCLOAK
          });
        }

        // VERIFICAR SE USUÁRIO ATENDEU HOJE (LÓGICA EXISTENTE)
        let userData = user;
        if(user.profile?.key === 'WALLET_MANAGER' || user.profile?.key === 'ASSISTANT') {
          const queryRunner = this.dataSource.createQueryRunner();
          const todayStart = startOfDay(new Date());
          const todayEnd = endOfDay(new Date());
    
          const attendanceCount = await queryRunner.manager
            .getRepository(Attendance)
            .createQueryBuilder('attendance')
            .where('attendance.attendant_id = :id', { id: user.id })
            .andWhere('attendance.created_at BETWEEN :start AND :end', {
              start: todayStart,
              end: todayEnd,
            })
            .getCount();
    
          userData = {
            ...user,
            hasAttendedToday: attendanceCount > 0,
          } as any;
          
          await queryRunner.release();
        }

        // RETORNAR DADOS DO KEYCLOAK + INFORMAÇÕES DO USUÁRIO LOCAL
        return {
          access_token: data.access_token,
          refresh_token: data.refresh_token,
          token_type: data.token_type,
          expires_in: data.expires_in,
          id_token: data.id_token,
          scope: data.scope,
          user: userData,
          keycloak_user_info: userInfo,
        } as any;
      } catch (error) {
        console.log('Erro ao buscar/criar usuário local:', error);
        // MESMO SE DER ERRO NO BANCO LOCAL, RETORNA OS DADOS DO KEYCLOAK
        return {
          access_token: data.access_token,
          refresh_token: data.refresh_token,
          token_type: data.token_type,
          expires_in: data.expires_in,
          id_token: data.id_token,
          scope: data.scope,
          user: {
            id: decodedToken.sub,
            email: userInfo.email,
            name: userInfo.name,
            username: decodedToken.preferred_username,
            roles: decodedToken.roles,
          },
          keycloak_user_info: userInfo,
        } as any;
      }
    } catch (error) {
      console.error('Erro no login Keycloak:', error?.response?.data || error.message);
      throw new UnauthorizedException('Invalid credentials');
    }
  }
  
  async refreshAccessToken(refreshToken: string): Promise<any> {
    try {
      const { data, status } = await firstValueFrom(
        this.http.post(
          `${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/token`,
          new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: this.keycloakClientId,
            refresh_token: refreshToken,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        ),
      );
      
      if (status !== 200) {
        throw new UnauthorizedException('Invalid refresh token');
      }
      
      return {
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        token_type: data.token_type,
        expires_in: data.expires_in,
        id_token: data.id_token,
        scope: data.scope,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async validateToken(token: string): Promise<any> {
    try {
      // VALIDAR TOKEN NO KEYCLOAK DA SICREDI
      const userInfo = await this.getUserInfo(token);
      return userInfo;
    } catch (e) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  async findByToken(token: string): Promise<any> {
    try {
      // DECODIFICAR TOKEN
      const decodedToken = jwtDecode(token) as any;
      if (!decodedToken?.preferred_username) {
        throw new UnauthorizedException('Invalid Access Token');
      }

      // BUSCAR USUÁRIO NO BANCO LOCAL
      const user = await this.usersService.findByKeycloakUsername(decodedToken.preferred_username);
      
      if (user) {
        return user;
      }

      // SE NÃO ENCONTRAR NO BANCO LOCAL, BUSCAR NO KEYCLOAK
      const userInfo = await this.getUserInfo(token);

      // EXTRAIR ROLES DO TOKEN CORRETAMENTE
      const keycloakRoles = this.extractRolesFromToken(decodedToken);

      return {
        id: decodedToken.sub,
        email: userInfo.email,
        name: userInfo.name,
        username: decodedToken.preferred_username,
        roles: keycloakRoles,
        keycloak_user_info: userInfo,
      };
    } catch (e) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  // BUSCAR INFORMAÇÕES DO USUÁRIO NO KEYCLOAK
  private async getUserInfo(accessToken: string): Promise<any> {
    try {
      const { data, status } = await firstValueFrom(
        this.http.get(
          `${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/userinfo`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
        ),
      );
      
      if (status !== 200) {
        throw new UnauthorizedException('Failed to get user info');
      }
      
      return data;
    } catch (error) {
      throw new UnauthorizedException('Failed to get user info');
    }
  }

  // MÉTODO AUXILIAR PARA EXTRAIR ROLES DO TOKEN KEYCLOAK
  private extractRolesFromToken(decodedToken: any): string[] {
    let keycloakRoles: string[] = [];

    // Tentar extrair roles de diferentes locais no token
    if (decodedToken.roles && Array.isArray(decodedToken.roles)) {
      keycloakRoles = [...keycloakRoles, ...decodedToken.roles];
    }

    if (decodedToken.realm_access?.roles && Array.isArray(decodedToken.realm_access.roles)) {
      keycloakRoles = [...keycloakRoles, ...decodedToken.realm_access.roles];
    }

    // Extrair roles de resource_access para o client específico
    if (decodedToken.resource_access) {
      Object.keys(decodedToken.resource_access).forEach(clientId => {
        const clientRoles = decodedToken.resource_access[clientId]?.roles;
        if (clientRoles && Array.isArray(clientRoles)) {
          keycloakRoles = [...keycloakRoles, ...clientRoles];
        }
      });
    }

    // Remover duplicatas
    return [...new Set(keycloakRoles)];
  }

  // LOGOUT NO KEYCLOAK
  async logout(refreshToken: string): Promise<boolean> {
    try {
      const { status } = await firstValueFrom(
        this.http.post(
          `${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/logout`,
          new URLSearchParams({
            client_id: this.keycloakClientId,
            refresh_token: refreshToken,
          }),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        ),
      );
      
      return status === 204 || status === 200;
    } catch (error) {
      console.error('Erro no logout:', error);
      return false;
    }
  }
}
