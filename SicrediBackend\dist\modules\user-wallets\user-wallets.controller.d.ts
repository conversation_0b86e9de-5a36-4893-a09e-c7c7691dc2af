import { UserWalletsService } from './user-wallets.service';
import { UserWallet } from './entities/user-wallets.entity';
export declare class UserWalletsController {
    private readonly userWalletsService;
    constructor(userWalletsService: UserWalletsService);
    create(userWalletDto: Partial<UserWallet>): Promise<UserWallet>;
    findAll(): Promise<UserWallet[]>;
    remove(id: number): Promise<void>;
}
