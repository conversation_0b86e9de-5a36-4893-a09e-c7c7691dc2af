"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const auth_controller_1 = require("./auth.controller");
const auth_service_1 = require("./auth.service");
const users_module_1 = require("../modules/users/users.module");
const axios_1 = require("@nestjs/axios");
const users_service_1 = require("../modules/users/users.service");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("../modules/users/entities/user.entity");
const cryptography_1 = require("../common/functions/cryptography");
const profile_permissions_service_1 = require("../modules/profile-permissions/profile-permissions.service");
const profile_permissions_module_1 = require("../modules/profile-permissions/profile-permissions.module");
const profile_permission_entity_1 = require("../modules/profile-permissions/entities/profile-permission.entity");
const profiles_module_1 = require("../modules/profiles/profiles.module");
const profiles_service_1 = require("../modules/profiles/profiles.service");
const profile_entity_1 = require("../modules/profiles/entities/profile.entity");
const cooperatives_service_1 = require("../modules/cooperatives/cooperatives.service");
const cooperatives_module_1 = require("../modules/cooperatives/cooperatives.module");
const centrals_module_1 = require("../modules/centrals/centrals.module");
const agencies_module_1 = require("../modules/agencies/agencies.module");
const federations_module_1 = require("../modules/federations/federations.module");
const attendance_entity_1 = require("../modules/attendances/entities/attendance.entity");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User, profile_permission_entity_1.ProfilePermission, profile_entity_1.Profile, attendance_entity_1.Attendance]),
            axios_1.HttpModule.register({
                timeout: 10000,
                maxRedirects: 5,
            }),
            users_module_1.UsersModule,
            profile_permissions_module_1.ProfilePermissionsModule,
            profiles_module_1.ProfilesModule,
            (0, common_1.forwardRef)(() => cooperatives_module_1.CooperativesModule),
            (0, common_1.forwardRef)(() => centrals_module_1.CentralsModule),
            (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule),
            (0, common_1.forwardRef)(() => federations_module_1.FederationsModule),
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [
            common_1.Logger,
            cryptography_1.Cryptography,
            auth_service_1.AuthService,
            users_service_1.UsersService,
            profile_permissions_service_1.ProfilePermissionsService,
            profiles_service_1.ProfilesService,
            cooperatives_service_1.CooperativesService,
        ],
        exports: [auth_service_1.AuthService],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map