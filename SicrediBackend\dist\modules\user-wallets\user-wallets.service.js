"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserWalletsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_wallets_entity_1 = require("./entities/user-wallets.entity");
const users_service_1 = require("../users/users.service");
let UserWalletsService = class UserWalletsService {
    constructor(userWalletRepository, usersService) {
        this.userWalletRepository = userWalletRepository;
        this.usersService = usersService;
    }
    async create(userWalletDto) {
        const { userId, walletId } = userWalletDto;
        const user = await this.usersService.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        const profileKey = user.profile?.key;
        if (!['WALLET_MANAGER', 'ASSISTANT'].includes(profileKey)) {
            throw new common_1.BadRequestException('Somente perfis de Gestor de Carteiras ou Administrador são permitidos');
        }
        const userWallet = this.userWalletRepository.create({
            userId,
            walletId,
            profileKey,
        });
        return this.userWalletRepository.save(userWallet);
    }
    async findAll() {
        return this.userWalletRepository.find({
            relations: ['user', 'wallet'],
        });
    }
    async remove(id) {
        const userWallet = await this.userWalletRepository.findOneBy({ id });
        if (!userWallet) {
            throw new common_1.NotFoundException('Associação não encontrada');
        }
        await this.userWalletRepository.update(id, { deletedAt: new Date() });
    }
    async findOneByUserAndWallet(idUser, idWallet) {
        return await this.userWalletRepository.findOne({
            where: { userId: idUser, walletId: idWallet, deletedAt: null },
        });
    }
};
exports.UserWalletsService = UserWalletsService;
exports.UserWalletsService = UserWalletsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_wallets_entity_1.UserWallet)),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => users_service_1.UsersService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        users_service_1.UsersService])
], UserWalletsService);
//# sourceMappingURL=user-wallets.service.js.map