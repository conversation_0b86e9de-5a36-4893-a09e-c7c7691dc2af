<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.sicrediApp"
    android:versionCode="1"
    android:versionName="1.0.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>

        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
        <intent>
            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
        </intent>
    </queries>
    <queries>
        <package android:name="com.google.android.googlequicksearchbox" />

        <intent>
            <action android:name="android.speech.RecognitionService" />
        </intent>
    </queries>

    <permission
        android:name="com.sicrediApp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.sicrediApp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.sicrediApp.MainApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true" >
        <meta-data
            android:name="expo.modules.updates.ENABLED"
            android:value="false" />
        <meta-data
            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
            android:value="ALWAYS" />
        <meta-data
            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
            android:value="0" />

        <activity
            android:name="com.sicrediApp.MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.App.SplashScreen"
            android:windowSoftInputMode="adjustResize" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="com.sicrediApp" />
                <data android:scheme="exp+teste-app" />
            </intent-filter>
        </activity>
        <activity
            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="expo-dev-launcher" />
            </intent-filter>
        </activity>
        <activity
            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
        <activity
            android:name="expo.modules.devmenu.DevMenuActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="expo-dev-menu" />
            </intent-filter>
        </activity>

        <provider
            android:name="expo.modules.filesystem.FileSystemFileProvider"
            android:authorities="com.sicrediApp.FileSystemFileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_system_provider_paths" />
        </provider>

        <meta-data
            android:name="org.unimodules.core.AppLoader#react-native-headless"
            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
        <meta-data
            android:name="com.facebook.soloader.enabled"
            android:value="true" />

        <activity
            android:name="com.facebook.react.devsupport.DevSettingsActivity"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.sicrediApp.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>