{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,208,280,350,432,500,567,640,719,803,889,958,1035,1116,1192,1273,1354,1430,1505,1580,1666,1736,1812,1886", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "126,203,275,345,427,495,562,635,714,798,884,953,1030,1111,1187,1268,1349,1425,1500,1575,1661,1731,1807,1881,1960"}, "to": {"startLines": "33,47,57,59,60,64,77,78,79,116,117,120,121,124,125,126,128,129,131,133,134,136,139,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2919,4248,5182,5324,5394,5706,6623,6690,6763,9737,9821,10082,10151,10383,10464,10540,10696,10777,10929,11080,11155,11342,11557,11756,11830", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "2990,4320,5249,5389,5471,5769,6685,6758,6837,9816,9902,10146,10223,10459,10535,10616,10772,10848,10999,11150,11236,11407,11628,11825,11904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "290,395,488,596,681,783,893,971,1048,1139,1232,1323,1417,1517,1610,1705,1799,1890,1981,2062,2165,2263,2361,2464,2570,2671,2824,10228", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "390,483,591,676,778,888,966,1043,1134,1227,1318,1412,1512,1605,1700,1794,1885,1976,2057,2160,2258,2356,2459,2565,2666,2819,2914,10305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,979,1068,1141,1216,1292,1368,1446,1513", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,974,1063,1136,1211,1287,1363,1441,1508,1631"}, "to": {"startLines": "45,46,49,50,51,61,62,113,114,118,119,123,127,130,132,137,138,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4085,4171,4428,4525,4626,5476,5561,9491,9577,9907,9993,10310,10621,10853,11004,11412,11490,11633", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "4166,4243,4520,4621,4709,5556,5641,9572,9655,9988,10077,10378,10691,10924,11075,11485,11552,11751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "35,36,37,38,39,40,41,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3078,3174,3277,3375,3473,3576,3681,11241", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3169,3272,3370,3468,3571,3676,3788,11337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "48,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4325,4874,4973,5084", "endColumns": "102,98,110,97", "endOffsets": "4423,4968,5079,5177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "11909,12006", "endColumns": "96,94", "endOffsets": "12001,12096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,240,323,422,535,615,685,775,845,905,992,1057,1118,1182,1243,1297,1398,1459,1519,1573,1643,1754,1841,1922,2035,2114,2196,2288,2355,2421,2491,2569,2655,2727,2805,2874,2943,3025,3113,3206,3300,3374,3443,3538,3590,3658,3743,3831,3893,3957,4020,4120,4213,4310,4403", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,112,78,81,91,66,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,76", "endOffsets": "235,318,417,530,610,680,770,840,900,987,1052,1113,1177,1238,1292,1393,1454,1514,1568,1638,1749,1836,1917,2030,2109,2191,2283,2350,2416,2486,2564,2650,2722,2800,2869,2938,3020,3108,3201,3295,3369,3438,3533,3585,3653,3738,3826,3888,3952,4015,4115,4208,4305,4398,4475"}, "to": {"startLines": "2,34,42,43,44,52,53,58,63,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2995,3793,3892,4005,4714,4784,5254,5646,5774,5861,5926,5987,6051,6112,6166,6267,6328,6388,6442,6512,6842,6929,7010,7123,7202,7284,7376,7443,7509,7579,7657,7743,7815,7893,7962,8031,8113,8201,8294,8388,8462,8531,8626,8678,8746,8831,8919,8981,9045,9108,9208,9301,9398,9660", "endLines": "5,34,42,43,44,52,53,58,63,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,112,78,81,91,66,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,76", "endOffsets": "285,3073,3887,4000,4080,4779,4869,5319,5701,5856,5921,5982,6046,6107,6161,6262,6323,6383,6437,6507,6618,6924,7005,7118,7197,7279,7371,7438,7504,7574,7652,7738,7810,7888,7957,8026,8108,8196,8289,8383,8457,8526,8621,8673,8741,8826,8914,8976,9040,9103,9203,9296,9393,9486,9732"}}]}]}