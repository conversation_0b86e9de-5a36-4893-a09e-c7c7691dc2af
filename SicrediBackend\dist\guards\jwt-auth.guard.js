"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const auth_service_1 = require("../auth/auth.service");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_decode_1 = require("jwt-decode");
let JwtAuthGuard = class JwtAuthGuard {
    constructor(authService, reflector) {
        this.authService = authService;
        this.reflector = reflector;
    }
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride(public_decorator_1.IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            throw new common_1.UnauthorizedException('Token not provided');
        }
        try {
            await this.authService.validateToken(token);
            const decodedToken = (0, jwt_decode_1.jwtDecode)(token);
            const user = await this.authService.findByToken(token);
            const keycloakRoles = this.extractRolesFromToken(decodedToken);
            request.user = {
                id: user.id,
                email: user.email || decodedToken.email,
                profileId: user.profileId,
                sub: user.id?.toString() || decodedToken.sub,
                agencyId: user.agencyId,
                cooperativeId: user.cooperativeId,
                centralId: user.centralId,
                federationId: user.federationId,
                profile: user.profile || {},
                keycloakId: decodedToken.sub,
                keycloakUsername: decodedToken.preferred_username,
                keycloakRoles: keycloakRoles,
                name: user.name || decodedToken.name,
                permissions: user.permissions || [],
                hierarchy: user.hierarchy,
                profileName: user.profileName,
                profileKey: user.profileKey,
            };
            return true;
        }
        catch (error) {
            console.error('Token validation error:', error);
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }
    extractRolesFromToken(decodedToken) {
        let keycloakRoles = [];
        if (decodedToken.roles && Array.isArray(decodedToken.roles)) {
            keycloakRoles = [...keycloakRoles, ...decodedToken.roles];
        }
        if (decodedToken.realm_access?.roles && Array.isArray(decodedToken.realm_access.roles)) {
            keycloakRoles = [...keycloakRoles, ...decodedToken.realm_access.roles];
        }
        if (decodedToken.resource_access) {
            Object.keys(decodedToken.resource_access).forEach(clientId => {
                const clientRoles = decodedToken.resource_access[clientId]?.roles;
                if (clientRoles && Array.isArray(clientRoles)) {
                    keycloakRoles = [...keycloakRoles, ...clientRoles];
                }
            });
        }
        return [...new Set(keycloakRoles)];
    }
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [auth_service_1.AuthService,
        core_1.Reflector])
], JwtAuthGuard);
//# sourceMappingURL=jwt-auth.guard.js.map