{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,304,401,516,598,663,751,815,876,966,1029,1091,1159,1223,1279,1402,1467,1529,1585,1656,1783,1867,1951,2057,2134,2211,2298,2365,2431,2507,2587,2676,2743,2817,2887,2953,3039,3109,3200,3290,3364,3437,3526,3577,3649,3730,3816,3878,3942,4005,4119,4222,4330,4433", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,96,114,81,64,87,63,60,89,62,61,67,63,55,122,64,61,55,70,126,83,83,105,76,76,86,66,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,79", "endOffsets": "220,299,396,511,593,658,746,810,871,961,1024,1086,1154,1218,1274,1397,1462,1524,1580,1651,1778,1862,1946,2052,2129,2206,2293,2360,2426,2502,2582,2671,2738,2812,2882,2948,3034,3104,3195,3285,3359,3432,3521,3572,3644,3725,3811,3873,3937,4000,4114,4217,4325,4428,4508"}, "to": {"startLines": "2,33,41,42,43,50,51,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2949,3745,3842,3957,4598,4663,5059,5298,5359,5449,5512,5574,5642,5706,5762,5885,5950,6012,6068,6139,6266,6350,6434,6540,6617,6694,6781,6848,6914,6990,7070,7159,7226,7300,7370,7436,7522,7592,7683,7773,7847,7920,8009,8060,8132,8213,8299,8361,8425,8488,8602,8705,8813,9087", "endLines": "5,33,41,42,43,50,51,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,106", "endColumns": "12,78,96,114,81,64,87,63,60,89,62,61,67,63,55,122,64,61,55,70,126,83,83,105,76,76,86,66,65,75,79,88,66,73,69,65,85,69,90,89,73,72,88,50,71,80,85,61,63,62,113,102,107,102,79", "endOffsets": "270,3023,3837,3952,4034,4658,4746,5118,5354,5444,5507,5569,5637,5701,5757,5880,5945,6007,6063,6134,6261,6345,6429,6535,6612,6689,6776,6843,6909,6985,7065,7154,7221,7295,7365,7431,7517,7587,7678,7768,7842,7915,8004,8055,8127,8208,8294,8356,8420,8483,8597,8700,8808,8911,9162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "118,119", "startColumns": "4,4", "startOffsets": "10069,10156", "endColumns": "86,86", "endOffsets": "10151,10238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "34,35,36,37,38,39,40,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3028,3123,3230,3327,3427,3530,3634,9703", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3118,3225,3322,3422,3525,3629,3740,9799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,375,472,584,669,770,884,965,1044,1135,1228,1321,1415,1521,1614,1709,1804,1895,1989,2070,2180,2287,2384,2493,2593,2696,2851,9328", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "370,467,579,664,765,879,960,1039,1130,1223,1316,1410,1516,1609,1704,1799,1890,1984,2065,2175,2282,2379,2488,2588,2691,2846,2944,9404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,984,1067,1137,1212,1287,1361,1438,1506", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,979,1062,1132,1207,1282,1356,1433,1501,1621"}, "to": {"startLines": "44,45,47,48,49,56,57,104,105,107,108,110,111,112,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4039,4130,4315,4414,4513,5123,5203,8916,9005,9167,9245,9409,9479,9554,9629,9804,9881,9949", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "4125,4206,4409,4508,4593,5198,5293,9000,9082,9240,9323,9474,9549,9624,9698,9876,9944,10064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "46,52,53,54", "startColumns": "4,4,4,4", "startOffsets": "4211,4751,4852,4958", "endColumns": "103,100,105,100", "endOffsets": "4310,4847,4953,5054"}}]}]}