import { AuthService } from './auth.service';
import { AuthResponseDto } from './dto/auth.dto';
import { SignInDto } from './dto/signIn.dto';
import { RefreshTokenDto } from './dto/refreshToken.dto';
import { UserDto } from 'src/modules/users/dto/user.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    signIn(signInDto: SignInDto): Promise<AuthResponseDto>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<any>;
    logout(body: {
        refreshToken: string;
    }): Promise<{
        message: string;
    }>;
    findByToken(authorization: string): Promise<UserDto>;
    testRoleMapping(body: {
        roles: string[];
    }): Promise<{
        roles: string[];
        mappedProfile: string;
        availableRoles: string[];
        availableProfiles: string[];
    }>;
}
