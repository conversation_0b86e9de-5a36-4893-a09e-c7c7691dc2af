{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "37,38,39,40,41,42,43,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3305,3403,3513,3612,3715,3826,3936,11818", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3398,3508,3607,3710,3821,3931,4051,11914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,330,413,509,627,711,777,876,954,1019,1129,1201,1260,1334,1395,1449,1573,1634,1696,1750,1828,1962,2050,2134,2245,2324,2408,2505,2572,2638,2713,2792,2880,2956,3034,3107,3184,3271,3352,3442,3534,3606,3687,3779,3834,3900,3985,4072,4134,4198,4261,4372,4487,4588,4702", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,82,95,117,83,65,98,77,64,109,71,58,73,60,53,123,60,61,53,77,133,87,83,110,78,83,96,66,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,81", "endOffsets": "325,408,504,622,706,772,871,949,1014,1124,1196,1255,1329,1390,1444,1568,1629,1691,1745,1823,1957,2045,2129,2240,2319,2403,2500,2567,2633,2708,2787,2875,2951,3029,3102,3179,3266,3347,3437,3529,3601,3682,3774,3829,3895,3980,4067,4129,4193,4256,4367,4482,4583,4697,4779"}, "to": {"startLines": "2,36,44,45,46,54,55,60,65,67,68,69,70,71,72,73,74,75,76,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3222,4056,4152,4270,5019,5085,5582,5986,6121,6231,6303,6362,6436,6497,6551,6675,6736,6798,6852,6930,7292,7380,7464,7575,7654,7738,7835,7902,7968,8043,8122,8210,8286,8364,8437,8514,8601,8682,8772,8864,8936,9017,9109,9164,9230,9315,9402,9464,9528,9591,9702,9817,9918,10203", "endLines": "7,36,44,45,46,54,55,60,65,67,68,69,70,71,72,73,74,75,76,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,117", "endColumns": "12,82,95,117,83,65,98,77,64,109,71,58,73,60,53,123,60,61,53,77,133,87,83,110,78,83,96,66,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,81", "endOffsets": "375,3300,4147,4265,4349,5080,5179,5655,6046,6226,6298,6357,6431,6492,6546,6670,6731,6793,6847,6925,7059,7375,7459,7570,7649,7733,7830,7897,7963,8038,8117,8205,8281,8359,8432,8509,8596,8677,8767,8859,8931,9012,9104,9159,9225,9310,9397,9459,9523,9586,9697,9812,9913,10027,10280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,213,286,357,444,514,582,660,742,824,905,979,1062,1146,1224,1307,1390,1466,1542,1616,1713,1788,1870,1943", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "123,208,281,352,439,509,577,655,737,819,900,974,1057,1141,1219,1302,1385,1461,1537,1611,1708,1783,1865,1938,2018"}, "to": {"startLines": "35,49,59,61,62,66,79,80,81,118,119,122,123,126,127,128,130,131,133,135,136,138,141,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3149,4531,5509,5660,5731,6051,7064,7132,7210,10285,10367,10622,10696,10938,11022,11100,11260,11343,11496,11647,11721,11919,12157,12361,12434", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "3217,4611,5577,5726,5813,6116,7127,7205,7287,10362,10443,10691,10774,11017,11095,11178,11338,11414,11567,11716,11813,11989,12234,12429,12509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,1005,1093,1168,1245,1322,1397,1477,1560", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,1000,1088,1163,1240,1317,1392,1472,1555,1677"}, "to": {"startLines": "47,48,51,52,53,63,64,115,116,120,121,125,129,132,134,139,140,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4354,4447,4721,4819,4924,5818,5895,10032,10119,10448,10534,10863,11183,11419,11572,11994,12074,12239", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "4442,4526,4814,4919,5014,5890,5981,10114,10198,10529,10617,10933,11255,11491,11642,12069,12152,12356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "380,496,600,713,800,902,1024,1107,1187,1281,1377,1474,1570,1673,1769,1867,1963,2057,2151,2234,2343,2451,2551,2661,2766,2872,3048,10779", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "491,595,708,795,897,1019,1102,1182,1276,1372,1469,1565,1668,1764,1862,1958,2052,2146,2229,2338,2446,2546,2656,2761,2867,3043,3144,10858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "50,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4616,5184,5289,5403", "endColumns": "104,104,113,105", "endOffsets": "4716,5284,5398,5504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "145,146", "startColumns": "4,4", "startOffsets": "12514,12602", "endColumns": "87,87", "endOffsets": "12597,12685"}}]}]}