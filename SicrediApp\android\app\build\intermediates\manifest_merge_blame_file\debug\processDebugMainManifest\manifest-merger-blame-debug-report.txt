1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sicrediApp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:5:3-75
11-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:5:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:3:3-77
13-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:3:20-75
14    <uses-permission android:name="android.permission.RECORD_AUDIO" />
14-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:4:3-68
14-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:4:20-66
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:6:3-63
15-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:6:20-61
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:7:3-78
16-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:7:20-76
17
18    <queries>
18-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:8:3-14:13
19        <intent>
19-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:9:5-13:14
20            <action android:name="android.intent.action.VIEW" />
20-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:7-58
20-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:15-56
21
22            <category android:name="android.intent.category.BROWSABLE" />
22-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:7-67
22-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:17-65
23
24            <data android:scheme="https" />
24-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:7-37
24-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:13-35
25        </intent>
26
27        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
27-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
27-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
28        <intent>
28-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
30        </intent>
31    </queries>
32    <queries>
32-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:8:3-14:13
33        <package android:name="com.google.android.googlequicksearchbox" />
34
35        <intent>
36            <action android:name="android.speech.RecognitionService" />
37        </intent>
38    </queries>
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b2df6d1b0f30613db0bc1444ee909f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.sicrediApp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b2df6d1b0f30613db0bc1444ee909f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b2df6d1b0f30613db0bc1444ee909f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.sicrediApp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b2df6d1b0f30613db0bc1444ee909f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b2df6d1b0f30613db0bc1444ee909f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
46-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:3-38:17
47        android:name="com.sicrediApp.MainApplication"
47-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:16-47
48        android:allowBackup="true"
48-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:162-188
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46b2df6d1b0f30613db0bc1444ee909f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="false"
52        android:icon="@mipmap/ic_launcher"
52-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:81-115
53        android:label="@string/app_name"
53-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:48-80
54        android:networkSecurityConfig="@xml/network_security_config"
54-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:248-308
55        android:roundIcon="@mipmap/ic_launcher_round"
55-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:116-161
56        android:supportsRtl="true"
56-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:221-247
57        android:theme="@style/AppTheme"
57-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:189-220
58        android:usesCleartextTraffic="true" >
58-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:21:309-344
59        <meta-data
59-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:22:5-83
60            android:name="expo.modules.updates.ENABLED"
60-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:22:16-59
61            android:value="false" />
61-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:22:60-81
62        <meta-data
62-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:23:5-105
63            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
63-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:23:16-80
64            android:value="ALWAYS" />
64-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:23:81-103
65        <meta-data
65-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:24:5-99
66            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
66-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:24:16-79
67            android:value="0" />
67-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:24:80-97
68
69        <activity
69-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:25:5-37:16
70            android:name="com.sicrediApp.MainActivity"
70-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:25:15-43
71            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
71-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:25:44-134
72            android:exported="true"
72-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:25:256-279
73            android:launchMode="singleTask"
73-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:25:135-166
74            android:theme="@style/Theme.App.SplashScreen"
74-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:25:210-255
75            android:windowSoftInputMode="adjustResize" >
75-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:25:167-209
76            <intent-filter>
76-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:26:7-29:23
77                <action android:name="android.intent.action.MAIN" />
77-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:27:9-60
77-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:27:17-58
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:28:9-68
79-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:28:19-66
80            </intent-filter>
81            <intent-filter>
81-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:30:7-36:23
82                <action android:name="android.intent.action.VIEW" />
82-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:7-58
82-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:15-56
83
84                <category android:name="android.intent.category.DEFAULT" />
84-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:32:9-67
84-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:32:19-65
85                <category android:name="android.intent.category.BROWSABLE" />
85-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:7-67
85-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:17-65
86
87                <data android:scheme="com.sicrediApp" />
87-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:7-37
87-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:13-35
88                <data android:scheme="exp+teste-app" />
88-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:7-37
88-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:13-35
89            </intent-filter>
90        </activity>
91        <activity
91-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
92            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
92-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
93            android:exported="true"
93-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
94            android:launchMode="singleTask"
94-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
95            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
95-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
96            <intent-filter>
96-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
97                <action android:name="android.intent.action.VIEW" />
97-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:7-58
97-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:15-56
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:32:9-67
99-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:32:19-65
100                <category android:name="android.intent.category.BROWSABLE" />
100-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:7-67
100-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:17-65
101
102                <data android:scheme="expo-dev-launcher" />
102-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:7-37
102-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:13-35
103            </intent-filter>
104        </activity>
105        <activity
105-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
106            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
106-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
107            android:screenOrientation="portrait"
107-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
108            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
108-->[:expo-dev-launcher] C:\Workspace\SicrediApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
109        <activity
109-->[:expo-dev-menu] C:\Workspace\SicrediApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
110            android:name="expo.modules.devmenu.DevMenuActivity"
110-->[:expo-dev-menu] C:\Workspace\SicrediApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
111            android:exported="true"
111-->[:expo-dev-menu] C:\Workspace\SicrediApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
112            android:launchMode="singleTask"
112-->[:expo-dev-menu] C:\Workspace\SicrediApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
113            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
113-->[:expo-dev-menu] C:\Workspace\SicrediApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
114            <intent-filter>
114-->[:expo-dev-menu] C:\Workspace\SicrediApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
115                <action android:name="android.intent.action.VIEW" />
115-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:7-58
115-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:10:15-56
116
117                <category android:name="android.intent.category.DEFAULT" />
117-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:32:9-67
117-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:32:19-65
118                <category android:name="android.intent.category.BROWSABLE" />
118-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:7-67
118-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:11:17-65
119
120                <data android:scheme="expo-dev-menu" />
120-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:7-37
120-->C:\Workspace\SicrediApp\android\app\src\main\AndroidManifest.xml:12:13-35
121            </intent-filter>
122        </activity>
123
124        <provider
124-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
125            android:name="expo.modules.filesystem.FileSystemFileProvider"
125-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
126            android:authorities="com.sicrediApp.FileSystemFileProvider"
126-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
127            android:exported="false"
127-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
128            android:grantUriPermissions="true" >
128-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
129            <meta-data
129-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-29:70
130                android:name="android.support.FILE_PROVIDER_PATHS"
130-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-67
131                android:resource="@xml/file_system_provider_paths" />
131-->[:expo-file-system] C:\Workspace\SicrediApp\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-67
132        </provider>
133
134        <meta-data
134-->[:expo-modules-core] C:\Workspace\SicrediApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
135            android:name="org.unimodules.core.AppLoader#react-native-headless"
135-->[:expo-modules-core] C:\Workspace\SicrediApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
136            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
136-->[:expo-modules-core] C:\Workspace\SicrediApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
137        <meta-data
137-->[:expo-modules-core] C:\Workspace\SicrediApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
138            android:name="com.facebook.soloader.enabled"
138-->[:expo-modules-core] C:\Workspace\SicrediApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
139            android:value="true" />
139-->[:expo-modules-core] C:\Workspace\SicrediApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
140
141        <activity
141-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ecdb51c6d8d0115905fab916a7c30fc\transformed\react-android-0.76.6-debug\AndroidManifest.xml:19:9-21:40
142            android:name="com.facebook.react.devsupport.DevSettingsActivity"
142-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ecdb51c6d8d0115905fab916a7c30fc\transformed\react-android-0.76.6-debug\AndroidManifest.xml:20:13-77
143            android:exported="false" />
143-->[com.facebook.react:react-android:0.76.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ecdb51c6d8d0115905fab916a7c30fc\transformed\react-android-0.76.6-debug\AndroidManifest.xml:21:13-37
144
145        <provider
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6117001b8a9c2e7108153dcdc609aa25\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
146            android:name="androidx.startup.InitializationProvider"
146-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6117001b8a9c2e7108153dcdc609aa25\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
147            android:authorities="com.sicrediApp.androidx-startup"
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6117001b8a9c2e7108153dcdc609aa25\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
148            android:exported="false" >
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6117001b8a9c2e7108153dcdc609aa25\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
149            <meta-data
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6117001b8a9c2e7108153dcdc609aa25\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.emoji2.text.EmojiCompatInitializer"
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6117001b8a9c2e7108153dcdc609aa25\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
151                android:value="androidx.startup" />
151-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6117001b8a9c2e7108153dcdc609aa25\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
152            <meta-data
152-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1baf2fc1c7da16dcc9ef41e6c8ebbf82\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
153                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
153-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1baf2fc1c7da16dcc9ef41e6c8ebbf82\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
154                android:value="androidx.startup" />
154-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1baf2fc1c7da16dcc9ef41e6c8ebbf82\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
155            <meta-data
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
156                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
157                android:value="androidx.startup" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
158        </provider>
159
160        <receiver
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
161            android:name="androidx.profileinstaller.ProfileInstallReceiver"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
162            android:directBootAware="false"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
163            android:enabled="true"
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
164            android:exported="true"
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
165            android:permission="android.permission.DUMP" >
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
166            <intent-filter>
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
167                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
168            </intent-filter>
169            <intent-filter>
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
170                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
171            </intent-filter>
172            <intent-filter>
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
173                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
174            </intent-filter>
175            <intent-filter>
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
176                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd840451f9fb5f12aa10daca7c70af1f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
177            </intent-filter>
178        </receiver>
179    </application>
180
181</manifest>
