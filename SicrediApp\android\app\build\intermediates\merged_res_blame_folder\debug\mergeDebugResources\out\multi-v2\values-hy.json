{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "47,53,54,55", "startColumns": "4,4,4,4", "startOffsets": "4343,4885,4988,5099", "endColumns": "103,102,110,102", "endOffsets": "4442,4983,5094,5197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,993,1081,1155,1232,1311,1388,1469,1538", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,988,1076,1150,1227,1306,1383,1464,1533,1651"}, "to": {"startLines": "44,45,48,49,50,59,60,109,110,112,113,115,116,117,119,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4085,4184,4447,4541,4641,5436,5518,9412,9507,9674,9759,9930,10004,10081,10235,10487,10568,10637", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,76,78,76,80,68,117", "endOffsets": "4179,4261,4536,4636,4719,5513,5599,9502,9584,9754,9842,9999,10076,10155,10307,10563,10632,10750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,378,478,588,677,783,900,982,1062,1153,1246,1341,1435,1535,1628,1723,1817,1908,1999,2082,2188,2294,2393,2503,2611,2712,2882,9847", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "373,473,583,672,778,895,977,1057,1148,1241,1336,1430,1530,1623,1718,1812,1903,1994,2077,2183,2289,2388,2498,2606,2707,2877,2974,9925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,395,525,606,670,767,852,914,1001,1065,1126,1193,1254,1308,1430,1487,1547,1601,1682,1817,1901,1986,2092,2167,2242,2337,2404,2470,2544,2624,2710,2781,2857,2933,3010,3098,3178,3274,3370,3444,3522,3622,3673,3742,3829,3920,3982,4046,4109,4214,4315,4415,4520", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "215,295,390,520,601,665,762,847,909,996,1060,1121,1188,1249,1303,1425,1482,1542,1596,1677,1812,1896,1981,2087,2162,2237,2332,2399,2465,2539,2619,2705,2776,2852,2928,3005,3093,3173,3269,3365,3439,3517,3617,3668,3737,3824,3915,3977,4041,4104,4209,4310,4410,4515,4600"}, "to": {"startLines": "2,33,41,42,43,51,52,56,61,63,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2979,3779,3874,4004,4724,4788,5202,5604,5734,5821,5885,5946,6013,6074,6128,6250,6307,6367,6421,6502,6709,6793,6878,6984,7059,7134,7229,7296,7362,7436,7516,7602,7673,7749,7825,7902,7990,8070,8166,8262,8336,8414,8514,8565,8634,8721,8812,8874,8938,9001,9106,9207,9307,9589", "endLines": "5,33,41,42,43,51,52,56,61,63,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,111", "endColumns": "12,79,94,129,80,63,96,84,61,86,63,60,66,60,53,121,56,59,53,80,134,83,84,105,74,74,94,66,65,73,79,85,70,75,75,76,87,79,95,95,73,77,99,50,68,86,90,61,63,62,104,100,99,104,84", "endOffsets": "265,3054,3869,3999,4080,4783,4880,5282,5661,5816,5880,5941,6008,6069,6123,6245,6302,6362,6416,6497,6632,6788,6873,6979,7054,7129,7224,7291,7357,7431,7511,7597,7668,7744,7820,7897,7985,8065,8161,8257,8331,8409,8509,8560,8629,8716,8807,8869,8933,8996,9101,9202,9302,9407,9669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "34,35,36,37,38,39,40,121", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3059,3159,3264,3362,3461,3566,3668,10386", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3154,3259,3357,3456,3561,3663,3774,10482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,281,349,421,496", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "127,195,276,344,416,491,565"}, "to": {"startLines": "46,57,58,62,75,118,120", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4266,5287,5355,5666,6637,10160,10312", "endColumns": "76,67,80,67,71,74,73", "endOffsets": "4338,5350,5431,5729,6704,10230,10381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,88", "endOffsets": "135,224"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "10755,10840", "endColumns": "84,88", "endOffsets": "10835,10924"}}]}]}