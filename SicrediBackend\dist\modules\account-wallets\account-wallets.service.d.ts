import { Repository } from 'typeorm';
import { AccountWallets } from './entities/account-wallets.entity';
import { CreateAccountWalletsDto } from './dto/create-account-wallets.dto';
import { AccountsService } from '../accounts/accounts.service';
import { WalletsService } from '../wallets/wallets.service';
import { Wallet } from '../wallets/entities/wallet.entity';
import { AgenciesService } from '../agencies/agencies.service';
export declare class AccountWalletsService {
    private readonly accountWalletsRepository;
    private readonly accountsService;
    private readonly agenciesService;
    private readonly walletService;
    private readonly walletsRepository;
    constructor(accountWalletsRepository: Repository<AccountWallets>, accountsService: AccountsService, agenciesService: AgenciesService, walletService: WalletsService, walletsRepository: Repository<Wallet>);
    createAccountWalletsFromBulk(accountWalletDto: CreateAccountWalletsDto[]): Promise<any>;
    private processAccountWalletBatch;
    deleteAccountWalletsFromBulk(accountWallets: {
        account_code: string;
        wallet_number: string;
        agency_code: string;
    }[]): Promise<any>;
}
