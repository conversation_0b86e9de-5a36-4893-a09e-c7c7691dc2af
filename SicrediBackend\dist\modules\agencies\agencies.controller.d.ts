import { AgenciesService } from './agencies.service';
import { CreateAgencyDto } from './dto/create-agency.dto';
import { UpdateAgencyDto } from './dto/update-agency.dto';
import { SearchAgencyDto } from './dto/search-agency.dto';
import { PaginatedAgencyDto } from './dto/paginated-agency.dto';
import { AgencyDto } from './dto/agency.dto';
export declare class AgenciesController {
    private readonly agenciesService;
    constructor(agenciesService: AgenciesService);
    create(createAgencyDto: CreateAgencyDto): Promise<CreateAgencyDto>;
    findAll(user: any): Promise<SearchAgencyDto[]>;
    findPaginatedAgencies(paginationParams: PaginatedAgencyDto, user: any): Promise<{
        items: any[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
    }>;
    update(id: string, updateAgencyDto: UpdateAgencyDto): Promise<CreateAgencyDto>;
    remove(id: string): Promise<void>;
    findAllByIdCooperative(id: string): Promise<SearchAgencyDto[]>;
    getAllAgencyCodes(): Promise<string[]>;
    findOne(id: string, user: any): Promise<SearchAgencyDto>;
    createBulk(agencyDto: AgencyDto[]): Promise<any>;
    deleteBulk(agencyCodes: string[]): Promise<any>;
}
