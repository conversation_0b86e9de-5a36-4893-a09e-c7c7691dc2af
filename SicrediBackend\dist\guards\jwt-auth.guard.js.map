{"version": 3, "file": "jwt-auth.guard.js", "sourceRoot": "", "sources": ["../../src/guards/jwt-auth.guard.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,uCAAyC;AACzC,uDAAmD;AACnD,4EAAsE;AACtE,2CAAuC;AAGhC,IAAM,YAAY,GAAlB,MAAM,YAAY;IACvB,YACmB,WAAwB,EACxB,SAAoB;QADpB,gBAAW,GAAX,WAAW,CAAa;QACxB,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAU,gCAAa,EAAE;YACxE,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,CAAC,QAAQ,EAAE;SACnB,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,8BAAqB,CAAC,oBAAoB,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAG5C,MAAM,YAAY,GAAG,IAAA,sBAAS,EAAC,KAAK,CAAQ,CAAC;YAG7C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAGvD,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAG/D,OAAO,CAAC,IAAI,GAAG;gBACb,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK;gBACvC,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,YAAY,CAAC,GAAG;gBAC5C,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAE3B,UAAU,EAAE,YAAY,CAAC,GAAG;gBAC5B,gBAAgB,EAAE,YAAY,CAAC,kBAAkB;gBACjD,aAAa,EAAE,aAAa;gBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI;gBAEpC,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC;YAEF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,IAAI,8BAAqB,CAAC,eAAe,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,OAAY;QACzC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACtE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/C,CAAC;IAGO,qBAAqB,CAAC,YAAiB;QAC7C,IAAI,aAAa,GAAa,EAAE,CAAC;QAGjC,IAAI,YAAY,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5D,aAAa,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,YAAY,CAAC,YAAY,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YACvF,aAAa,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACzE,CAAC;QAGD,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3D,MAAM,WAAW,GAAG,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC;gBAClE,IAAI,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC9C,aAAa,GAAG,CAAC,GAAG,aAAa,EAAE,GAAG,WAAW,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAGD,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;IACrC,CAAC;CACF,CAAA;AAjGY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;qCAGqB,0BAAW;QACb,gBAAS;GAH5B,YAAY,CAiGxB"}