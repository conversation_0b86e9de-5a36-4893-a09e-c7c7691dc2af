{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "35,36,37,38,39,40,41,135", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3182,3280,3383,3484,3590,3691,3799,11588", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3275,3378,3479,3585,3686,3794,3922,11684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,306,413,526,611,674,768,834,896,999,1070,1129,1205,1270,1324,1437,1495,1556,1610,1689,1805,1888,1979,2091,2170,2249,2337,2404,2470,2550,2640,2724,2801,2878,2955,3024,3123,3200,3293,3388,3462,3543,3639,3690,3758,3844,3932,3995,4060,4123,4228,4331,4426,4531", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "219,301,408,521,606,669,763,829,891,994,1065,1124,1200,1265,1319,1432,1490,1551,1605,1684,1800,1883,1974,2086,2165,2244,2332,2399,2465,2545,2635,2719,2796,2873,2950,3019,3118,3195,3288,3383,3457,3538,3634,3685,3753,3839,3927,3990,4055,4118,4223,4326,4421,4526,4608"}, "to": {"startLines": "2,34,42,43,44,52,53,58,63,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3100,3927,4034,4147,4891,4954,5437,5830,5960,6063,6134,6193,6269,6334,6388,6501,6559,6620,6674,6753,7086,7169,7260,7372,7451,7530,7618,7685,7751,7831,7921,8005,8082,8159,8236,8305,8404,8481,8574,8669,8743,8824,8920,8971,9039,9125,9213,9276,9341,9404,9509,9612,9707,9988", "endLines": "5,34,42,43,44,52,53,58,63,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,115", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "269,3177,4029,4142,4227,4949,5043,5498,5887,6058,6129,6188,6264,6329,6383,6496,6554,6615,6669,6748,6864,7164,7255,7367,7446,7525,7613,7680,7746,7826,7916,8000,8077,8154,8231,8300,8399,8476,8569,8664,8738,8819,8915,8966,9034,9120,9208,9271,9336,9399,9504,9607,9702,9807,10065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "143,144", "startColumns": "4,4", "startOffsets": "12247,12335", "endColumns": "87,94", "endOffsets": "12330,12425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,388,500,613,701,808,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2107,2223,2333,2432,2545,2650,2764,2928,10556", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "383,495,608,696,803,929,1007,1083,1174,1267,1362,1456,1556,1649,1744,1838,1929,2020,2102,2218,2328,2427,2540,2645,2759,2923,3023,10634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,1006,1092,1171,1246,1324,1401,1478,1547", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,1001,1087,1166,1241,1319,1396,1473,1542,1660"}, "to": {"startLines": "45,46,49,50,51,61,62,113,114,118,119,123,127,130,132,137,138,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4232,4329,4606,4702,4802,5653,5737,9812,9903,10236,10318,10639,10958,11197,11346,11761,11838,11985", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "4324,4408,4697,4797,4886,5732,5825,9898,9983,10313,10399,10713,11028,11270,11418,11833,11902,12098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,347,428,496,563,638,713,798,879,950,1031,1111,1189,1271,1358,1435,1506,1576,1671,1743,1821,1890", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "122,201,273,342,423,491,558,633,708,793,874,945,1026,1106,1184,1266,1353,1430,1501,1571,1666,1738,1816,1885,1960"}, "to": {"startLines": "33,47,57,59,60,64,77,78,79,116,117,120,121,124,125,126,128,129,131,133,134,136,139,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3028,4413,5365,5503,5572,5892,6869,6936,7011,10070,10155,10404,10475,10718,10798,10876,11033,11120,11275,11423,11493,11689,11907,12103,12172", "endColumns": "71,78,71,68,80,67,66,74,74,84,80,70,80,79,77,81,86,76,70,69,94,71,77,68,74", "endOffsets": "3095,4487,5432,5567,5648,5955,6931,7006,7081,10150,10231,10470,10551,10793,10871,10953,11115,11192,11341,11488,11583,11756,11980,12167,12242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "48,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4492,5048,5148,5264", "endColumns": "113,99,115,100", "endOffsets": "4601,5143,5259,5360"}}]}]}