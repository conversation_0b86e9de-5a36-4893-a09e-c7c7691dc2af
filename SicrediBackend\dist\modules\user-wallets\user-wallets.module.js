"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserWalletsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_wallets_entity_1 = require("./entities/user-wallets.entity");
const user_wallets_service_1 = require("./user-wallets.service");
const user_wallets_controller_1 = require("./user-wallets.controller");
const user_entity_1 = require("../users/entities/user.entity");
const users_module_1 = require("../users/users.module");
const users_service_1 = require("../users/users.service");
const keycloak_module_1 = require("../keycloak/keycloak.module");
const keycloak_entity_1 = require("../keycloak/entities/keycloak.entity");
const keycloak_service_1 = require("../keycloak/keycloak.service");
const profiles_module_1 = require("../profiles/profiles.module");
const cryptography_1 = require("../../common/functions/cryptography");
const profile_permissions_service_1 = require("../profile-permissions/profile-permissions.service");
const profile_permission_entity_1 = require("../profile-permissions/entities/profile-permission.entity");
const profile_entity_1 = require("../profiles/entities/profile.entity");
const axios_1 = require("@nestjs/axios");
const profile_permissions_module_1 = require("../profile-permissions/profile-permissions.module");
const cooperatives_module_1 = require("../cooperatives/cooperatives.module");
const centrals_module_1 = require("../centrals/centrals.module");
const agencies_module_1 = require("../agencies/agencies.module");
const federations_module_1 = require("../federations/federations.module");
let UserWalletsModule = class UserWalletsModule {
};
exports.UserWalletsModule = UserWalletsModule;
exports.UserWalletsModule = UserWalletsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_wallets_entity_1.UserWallet, user_entity_1.User, keycloak_entity_1.Keycloak, profile_permission_entity_1.ProfilePermission, profile_entity_1.Profile]),
            (0, common_1.forwardRef)(() => cooperatives_module_1.CooperativesModule),
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            (0, common_1.forwardRef)(() => centrals_module_1.CentralsModule),
            (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule),
            (0, common_1.forwardRef)(() => federations_module_1.FederationsModule),
            keycloak_module_1.KeycloakModule,
            profiles_module_1.ProfilesModule,
            axios_1.HttpModule,
            profile_permissions_module_1.ProfilePermissionsModule,
        ],
        providers: [user_wallets_service_1.UserWalletsService,
            users_service_1.UsersService,
            keycloak_service_1.KeycloakService,
            common_1.Logger,
            cryptography_1.Cryptography,
            profile_permissions_service_1.ProfilePermissionsService,
        ],
        controllers: [user_wallets_controller_1.UserWalletsController],
        exports: [user_wallets_service_1.UserWalletsService, typeorm_1.TypeOrmModule],
    })
], UserWalletsModule);
//# sourceMappingURL=user-wallets.module.js.map