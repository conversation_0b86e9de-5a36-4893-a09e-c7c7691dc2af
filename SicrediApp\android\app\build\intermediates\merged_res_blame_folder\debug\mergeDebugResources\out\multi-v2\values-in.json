{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "46,52,53,54", "startColumns": "4,4,4,4", "startOffsets": "4264,4807,4905,5014", "endColumns": "99,97,108,100", "endOffsets": "4359,4900,5009,5110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "34,35,36,37,38,39,40,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3057,3152,3254,3351,3448,3554,3672,9758", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3147,3249,3346,3443,3549,3667,3782,9854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,989,1072,1144,1221,1298,1371,1449,1515", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,76,76,72,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,984,1067,1139,1216,1293,1366,1444,1510,1629"}, "to": {"startLines": "44,45,47,48,49,56,57,104,105,107,108,110,111,112,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4092,4182,4364,4462,4562,5180,5263,8957,9044,9209,9291,9459,9531,9608,9685,9859,9937,10003", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,76,76,72,77,65,118", "endOffsets": "4177,4259,4457,4557,4643,5258,5349,9039,9124,9286,9369,9526,9603,9680,9753,9932,9998,10117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,303,409,525,608,673,767,832,891,978,1040,1100,1166,1228,1282,1394,1451,1512,1566,1638,1764,1850,1934,2043,2124,2205,2295,2362,2428,2500,2584,2667,2742,2818,2891,2966,3051,3126,3218,3312,3386,3459,3553,3605,3674,3759,3846,3908,3972,4035,4138,4238,4333,4435", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "219,298,404,520,603,668,762,827,886,973,1035,1095,1161,1223,1277,1389,1446,1507,1561,1633,1759,1845,1929,2038,2119,2200,2290,2357,2423,2495,2579,2662,2737,2813,2886,2961,3046,3121,3213,3307,3381,3454,3548,3600,3669,3754,3841,3903,3967,4030,4133,4233,4328,4430,4510"}, "to": {"startLines": "2,33,41,42,43,50,51,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2978,3787,3893,4009,4648,4713,5115,5354,5413,5500,5562,5622,5688,5750,5804,5916,5973,6034,6088,6160,6286,6372,6456,6565,6646,6727,6817,6884,6950,7022,7106,7189,7264,7340,7413,7488,7573,7648,7740,7834,7908,7981,8075,8127,8196,8281,8368,8430,8494,8557,8660,8760,8855,9129", "endLines": "5,33,41,42,43,50,51,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,106", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "269,3052,3888,4004,4087,4708,4802,5175,5408,5495,5557,5617,5683,5745,5799,5911,5968,6029,6083,6155,6281,6367,6451,6560,6641,6722,6812,6879,6945,7017,7101,7184,7259,7335,7408,7483,7568,7643,7735,7829,7903,7976,8070,8122,8191,8276,8363,8425,8489,8552,8655,8755,8850,8952,9204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "118,119", "startColumns": "4,4", "startOffsets": "10122,10208", "endColumns": "85,89", "endOffsets": "10203,10293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,389,493,598,685,789,905,988,1066,1157,1250,1345,1439,1539,1632,1727,1821,1912,2003,2089,2192,2297,2398,2502,2611,2719,2879,9374", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "384,488,593,680,784,900,983,1061,1152,1245,1340,1434,1534,1627,1722,1816,1907,1998,2084,2187,2292,2393,2497,2606,2714,2874,2973,9454"}}]}]}