import { CreateGoalDto } from './dto/create-goal.dto';
import { Goal } from './entities/goal.entity';
import { DataSource, Repository } from 'typeorm';
import { GoalProductWallet } from '../goal-product-wallet/entities/goal-product-wallet.entity';
import { PaginatedGoalsDto } from './dto/paginated-goal.dto';
import { UpdateGoalDto } from './dto/update-strategy.dto';
import { User } from '../users/entities/user.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { GoalDto } from './dto/goal.dto';
import { Agency } from '../agencies/entities/agency.entity';
import { Product } from '../products/entities/product.entity';
export declare class GoalService {
    private readonly goalRepository;
    private readonly userRepository;
    private readonly walletRepository;
    private readonly goalProductWalletRepository;
    private readonly agencyRepository;
    private readonly productRepository;
    private readonly dataSource;
    constructor(goalRepository: Repository<Goal>, userRepository: Repository<User>, walletRepository: Repository<Wallet>, goalProductWalletRepository: Repository<GoalProductWallet>, agencyRepository: Repository<Agency>, productRepository: Repository<Product>, dataSource: DataSource);
    findPaginatedGoals(user: any, paginationParams: PaginatedGoalsDto): Promise<{
        items: CreateGoalDto[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
    }>;
    findOne(id: number): Promise<CreateGoalDto>;
    create(createGoalDto: CreateGoalDto): Promise<CreateGoalDto>;
    update(id: number, updateGoalDto: UpdateGoalDto): Promise<CreateGoalDto>;
    remove(id: number): Promise<void>;
    createGoalFromBulk(goalDto: GoalDto[]): Promise<any>;
}
