import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthService } from '../auth/auth.service';
import { IS_PUBLIC_KEY } from '../common/decorators/public.decorator';
import { jwtDecode } from 'jwt-decode';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Token not provided');
    }

    try {
      // VALIDAR TOKEN COM O KEYCLOAK EXTERNO DA SICREDI
      await this.authService.validateToken(token);
      
      // DECODIFICAR TOKEN PARA EXTRAIR INFORMAÇÕES
      const decodedToken = jwtDecode(token) as any;
      
      // BUSCAR USUÁRIO COMPLETO PELO TOKEN
      const user = await this.authService.findByToken(token);

      // EXTRAIR ROLES DO TOKEN CORRETAMENTE
      const keycloakRoles = this.extractRolesFromToken(decodedToken);

      // ADICIONAR INFORMAÇÕES DO USUÁRIO AO REQUEST
      request.user = {
        id: user.id,
        email: user.email || decodedToken.email,
        profileId: user.profileId,
        sub: user.id?.toString() || decodedToken.sub,
        agencyId: user.agencyId,
        cooperativeId: user.cooperativeId,
        centralId: user.centralId,
        federationId: user.federationId,
        profile: user.profile || {},
        // INFORMAÇÕES DO KEYCLOAK
        keycloakId: decodedToken.sub,
        keycloakUsername: decodedToken.preferred_username,
        keycloakRoles: keycloakRoles,
        name: user.name || decodedToken.name,
        // ADICIONAR PERMISSÕES E DADOS DO PERFIL
        permissions: user.permissions || [],
        hierarchy: user.hierarchy,
        profileName: user.profileName,
        profileKey: user.profileKey,
      };

      return true;
    } catch (error) {
      console.error('Token validation error:', error);
      throw new UnauthorizedException('Invalid token');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  // MÉTODO AUXILIAR PARA EXTRAIR ROLES DO TOKEN KEYCLOAK
  private extractRolesFromToken(decodedToken: any): string[] {
    let keycloakRoles: string[] = [];

    // Tentar extrair roles de diferentes locais no token
    if (decodedToken.roles && Array.isArray(decodedToken.roles)) {
      keycloakRoles = [...keycloakRoles, ...decodedToken.roles];
    }

    if (decodedToken.realm_access?.roles && Array.isArray(decodedToken.realm_access.roles)) {
      keycloakRoles = [...keycloakRoles, ...decodedToken.realm_access.roles];
    }

    // Extrair roles de resource_access para o client específico
    if (decodedToken.resource_access) {
      Object.keys(decodedToken.resource_access).forEach(clientId => {
        const clientRoles = decodedToken.resource_access[clientId]?.roles;
        if (clientRoles && Array.isArray(clientRoles)) {
          keycloakRoles = [...keycloakRoles, ...clientRoles];
        }
      });
    }

    // Remover duplicatas
    return [...new Set(keycloakRoles)];
  }
}