import { UsersService } from './users.service';
import { UserDto } from './dto/user.dto';
import { CreateUserResponse } from './dto/createUserResponse.dto';
import { CreateUserDto } from './dto/createUser.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
import { SearchUserDto } from './dto/searchUser.dto';
import { PaginatedUsersDto } from './dto/paginatedUser.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    searchByName(searchUser?: SearchUserDto): Promise<UserDto[]>;
    findById(id: number): Promise<UserDto>;
    create(user: CreateUserDto, request: any): Promise<CreateUserResponse>;
    update(id: number, user: UpdateUserDto, request: any): Promise<{
        message: string;
    }>;
    remove(id: number, request: any): Promise<void>;
    findAll(): Promise<UserDto[]>;
    findAllByAgency(id: number, filter?: string): Promise<UserDto[]>;
    findPaginatedUsers(user: any, paginationParams: PaginatedUsersDto): Promise<{
        items: UserDto[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
    }>;
    createBulk(userDto: UserDto[], request: any): Promise<any>;
}
