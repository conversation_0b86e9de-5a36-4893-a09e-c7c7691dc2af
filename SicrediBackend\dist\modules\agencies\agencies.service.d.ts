import { CreateAgencyDto } from './dto/create-agency.dto';
import { UpdateAgencyDto } from './dto/update-agency.dto';
import { Agency } from './entities/agency.entity';
import { Repository } from 'typeorm';
import { SearchAgencyDto } from './dto/search-agency.dto';
import { PaginatedAgencyDto } from './dto/paginated-agency.dto';
import { WalletRangeValuesService } from '../wallet-range-values/wallet-range-values.service';
import { WalletsService } from '../wallets/wallets.service';
import { CooperativesService } from '../cooperatives/cooperatives.service';
import { CentralsService } from '../centrals/centrals.service';
import { AgencyDto } from './dto/agency.dto';
import { Cooperative } from '../cooperatives/entities/cooperative.entity';
export declare class AgenciesService {
    private readonly agencyRepository;
    private readonly cooperativeRepository;
    private readonly walletRangeValuesService;
    private readonly walletService;
    private readonly cooperativeService;
    private readonly centralService;
    constructor(agencyRepository: Repository<Agency>, cooperativeRepository: Repository<Cooperative>, walletRangeValuesService: WalletRangeValuesService, walletService: WalletsService, cooperativeService: CooperativesService, centralService: CentralsService);
    create(createAgencyDto: CreateAgencyDto): Promise<CreateAgencyDto>;
    findByName(name: string): Promise<CreateAgencyDto | null>;
    findAll(user: any): Promise<SearchAgencyDto[]>;
    findOne(identifier: string | number): Promise<CreateAgencyDto>;
    findByCodes(codes: string[]): Promise<Agency[]>;
    update(id: number, updateData: UpdateAgencyDto): Promise<CreateAgencyDto>;
    remove(id: number): Promise<void>;
    findAllByIdCooperative(id: number): Promise<SearchAgencyDto[]>;
    findPaginatedAgencies(paginationParams: PaginatedAgencyDto, user: any): Promise<{
        items: any[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
    }>;
    findAllByCentral(id: number): Promise<SearchAgencyDto[]>;
    findAllByFederation(federationId: number): Promise<SearchAgencyDto[]>;
    getAllAgencyCodes(): Promise<string[]>;
    findOneUser(id: number, user: any): Promise<SearchAgencyDto>;
    createAgencyFromBulk(agencyDto: AgencyDto[]): Promise<any>;
    deleteAgenciesFromBulk(agencyCodes: string[]): Promise<any>;
}
