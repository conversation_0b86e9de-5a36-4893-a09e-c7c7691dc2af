{"name": "SicrediApp", "slug": "teste-app", "version": "1.0.0", "extra": {"eas": {"projectId": "582e7b4a-814c-4923-a370-a3886151c8f0"}}, "plugins": [["expo-font", {"fonts": ["./src/assets/fonts/Calibri.ttf", "./src/assets/fonts/Exo2.0-Regular.otf", "./src/assets/fonts/Exo2.0-Bold.otf", "./src/assets/fonts/Nunito-Regular.ttf", "./src/assets/fonts/Nunito-Light.ttf"]}], ["expo-dev-launcher", {"launchMode": "most-recent"}], "expo-dev-client", ["expo-speech-recognition", {"microphonePermission": "Allow $(PRODUCT_NAME) to use the microphone.", "speechRecognitionPermission": "Allow $(PRODUCT_NAME) to use speech recognition.", "androidSpeechServicePackages": ["com.google.android.googlequicksearchbox"]}]], "android": {"permissions": ["android.permission.RECORD_AUDIO"], "package": "com.sicrediApp"}, "ios": {"bundleIdentifier": "com.sicrediApp", "infoPlist": {"NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to use the microphone.", "NSSpeechRecognitionUsageDescription": "Allow $(PRODUCT_NAME) to use speech recognition."}}, "newArchEnabled": false, "sdkVersion": "52.0.0", "platforms": ["ios", "android", "web"]}