import { CreateWalletDto } from './dto/create-wallet.dto';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { Repository } from 'typeorm';
import { Wallet } from './entities/wallet.entity';
import { SearchWalletDto } from './dto/search-wallet.dto';
import { SegmentsService } from '../segments/segments.service';
import { WalletRangeValuesService } from '../wallet-range-values/wallet-range-values.service';
import { ToCreateWalletDto } from './dto/to-create-wallet.dto';
import { Associate } from '../associates/entities/associate.entity';
import { AssociatesService } from '../associates/associates.service';
import { UserWalletsService } from '../user-wallets/user-wallets.service';
import { UsersService } from '../users/users.service';
import { AgenciesService } from '../agencies/agencies.service';
import { CreateAgencyDto } from '../agencies/dto/create-agency.dto';
import { UserDto } from '../users/dto/user.dto';
import { FindAllUsersAndWalletRangeResponseDto } from './dto/find-all-users-and-wallet-range-response.dto';
import { PaginatedWalletNumberAssociateDto } from './dto/paginated-wallet-number-associate.dto';
import { WalletDto } from './dto/wallet.dto';
import { Cryptography } from 'src/common/functions/cryptography';
import { ResponseAccountDto } from '../accounts/dto/response-account.dto';
import { Accounts } from '../accounts/entities/account.entity';
import { AccountWallets } from '../account-wallets/entities/account-wallets.entity';
import { GoalProductWallet } from '../goal-product-wallet/entities/goal-product-wallet.entity';
import { Agency } from '../agencies/entities/agency.entity';
import { WalletRangeValue } from '../wallet-range-values/entities/wallet-range-value.entity';
import { User } from '../users/entities/user.entity';
import { Segment } from '../segments/entities/segment.entity';
export declare class WalletsService {
    private readonly walletRepository;
    private readonly accountsRepository;
    private readonly accountWalletsRepository;
    private readonly goalProductWalletRepository;
    private readonly agenciesRepository;
    private readonly walletRangeValueRepository;
    private readonly segmentService;
    private readonly segmentRepository;
    private readonly userRepository;
    private readonly walletRangeService;
    private readonly associatesService;
    private readonly userWalletsService;
    private readonly usersService;
    private readonly agenciesService;
    private readonly cryptography;
    constructor(walletRepository: Repository<Wallet>, accountsRepository: Repository<Accounts>, accountWalletsRepository: Repository<AccountWallets>, goalProductWalletRepository: Repository<GoalProductWallet>, agenciesRepository: Repository<Agency>, walletRangeValueRepository: Repository<WalletRangeValue>, segmentService: SegmentsService, segmentRepository: Repository<Segment>, userRepository: Repository<User>, walletRangeService: WalletRangeValuesService, associatesService: AssociatesService, userWalletsService: UserWalletsService, usersService: UsersService, agenciesService: AgenciesService, cryptography: Cryptography);
    create(createWalletDto: CreateWalletDto): Promise<CreateWalletDto>;
    findAll(): Promise<CreateWalletDto[]>;
    findAllToUpdateWallet(id: number, req: any): Promise<{
        wallet: Wallet;
        associates: Associate[];
        agency: CreateAgencyDto;
        usersToWallet: UserDto[];
    }>;
    findAllToCreateWallet(userReq: any): Promise<ToCreateWalletDto>;
    findOne(id: number): Promise<{
        wallet: Wallet;
        associates: Associate[];
        agency: CreateAgencyDto;
    }>;
    update(id: number, updateWalletDto: UpdateWalletDto): Promise<UpdateWalletDto>;
    remove(id: number): Promise<string>;
    findNumberWallet({ segmentId, walletRangeId, agencyId, }: SearchWalletDto): Promise<{
        number: string;
        numberOld: string;
    }>;
    calculateWalletNumbers(segmentId: number, walletRangeId: number, agencyId: number): Promise<{
        number: string;
        numberOld: string;
        segmenteNumber: string;
        walletRangeNumber: string;
        numberWallet: string;
        segmenteNumberOld: string;
        walletRangeNumberOld: string;
        numberOldWallet: string;
    }>;
    findAllByCooperativeId(id: number): Promise<CreateWalletDto[]>;
    findAllByAgencyId(id: number): Promise<CreateWalletDto[]>;
    findAllByUserId(id: number): Promise<CreateWalletDto[]>;
    findAllPaged(userReq: any, page: number, pageSize: number, segmentId?: number, walletRangeId?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByCooperativeIdPaged(cooperativeId: number, page?: number, limit?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByUserIdPaged(userReq: any, id: number, page: number, pageSize: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByAgencyIdPaged(id: number, page: number, pageSize: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: PaginatedWalletNumberAssociateDto[];
    }>;
    findAllUsersAndWalletRangeValuesByAgency(id: number): Promise<FindAllUsersAndWalletRangeResponseDto>;
    createDefaultWalletsByAgency(idAgency: number): Promise<void>;
    findAllByCentralIdPaged(centralId: number, page?: number, limit?: number, segmentId?: number, walletRangeId?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByFederationIdPaged(federationId: number, page?: number, limit?: number, segmentId?: number, walletRangeId?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: PaginatedWalletNumberAssociateDto[];
    }>;
    findAllPagedAndFiltered(userReq: any, page?: number, limit?: number, segmentId?: number, walletRangeId?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: PaginatedWalletNumberAssociateDto[];
    }>;
    createWalletFromBulk(walletsData: WalletDto[]): Promise<any>;
    private processWalletBatch;
    deleteWalletsFromBulk(walletsData: {
        wallet_number: string;
        agency_code: string;
    }[]): Promise<any>;
    findAllByAssociateAndAgency(associateId: number, user: any): Promise<CreateWalletDto[]>;
    findAllAccountsByAgencyId(agencyId: number): Promise<ResponseAccountDto[]>;
    getWalletRangeValue(number: string, numberOld: string): Promise<string | null>;
    findByNumber(walletNumber: string, agencyCode: string): Promise<Wallet | null>;
}
