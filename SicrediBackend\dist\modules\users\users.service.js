"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UsersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const user_entity_1 = require("./entities/user.entity");
const cryptography_1 = require("../../common/functions/cryptography");
const profile_permissions_service_1 = require("../profile-permissions/profile-permissions.service");
const profiles_service_1 = require("../profiles/profiles.service");
const cooperatives_service_1 = require("../cooperatives/cooperatives.service");
const queryHelper_1 = require("../../common/functions/queryHelper");
const centrals_service_1 = require("../centrals/centrals.service");
const agencies_service_1 = require("../agencies/agencies.service");
const federations_service_1 = require("../federations/federations.service");
const keycloak_role_mapper_1 = require("../../utils/keycloak-role-mapper");
let UsersService = UsersService_1 = class UsersService {
    constructor(usersRepository, logger, profileService, cooperativeService, centralService, agenciesSevice, profilePermissionsService, federationsService, cryptography) {
        this.usersRepository = usersRepository;
        this.logger = logger;
        this.profileService = profileService;
        this.cooperativeService = cooperativeService;
        this.centralService = centralService;
        this.agenciesSevice = agenciesSevice;
        this.profilePermissionsService = profilePermissionsService;
        this.federationsService = federationsService;
        this.cryptography = cryptography;
    }
    async update(id, user, token) {
        const profile = await this.profileService.findByKey(user.profileKey);
        const userToUpdate = await this.findById(id);
        userToUpdate.name = user.name;
        userToUpdate.phone = this.cryptography.encrypt(user.phone);
        userToUpdate.email = this.cryptography.encrypt(user.email);
        userToUpdate.profileId = profile.id;
        userToUpdate.agencyId = user.agencyId;
        userToUpdate.centralId = user.centralId;
        userToUpdate.federationId = user.federationId;
        userToUpdate.cnpj = user.cnpj;
        userToUpdate.cpf = this.cryptography.encrypt(user.cpf);
        userToUpdate.cooperativeId = user.cooperativeId;
        userToUpdate.serviceUnitNumber = user.serviceUnitNumber;
        userToUpdate.birthDate = user.birthDate;
        userToUpdate.geralRegister = user.geralRegister;
        userToUpdate.updatedAt = new Date();
        delete userToUpdate.profile;
        await this.usersRepository.save(userToUpdate);
        return { message: 'Atualizado com sucesso!' };
    }
    async create(newUser, token) {
        let userNewProfile;
        const userAlreadyRegistered = await this.findByUserLogin(newUser.email);
        if (userAlreadyRegistered) {
            this.logger.error(`User '${newUser.email}' already registered`, { status: common_1.HttpStatus.CONFLICT }, UsersService_1.name);
            throw new common_1.ConflictException(`User '${newUser.email}' already registered`);
        }
        try {
            userNewProfile = await this.profileService.findByKey(newUser.profileKey);
            if (!userNewProfile?.id) {
                throw new common_1.NotFoundException('Profile not found');
            }
        }
        catch (error) {
            throw new common_1.BadRequestException('Profile not found');
        }
        try {
            const dbUser = new user_entity_1.User();
            dbUser.name = newUser.name;
            dbUser.lastName = newUser.lastName;
            dbUser.phone = this.cryptography.encrypt(newUser.phone);
            dbUser.email = this.cryptography.encrypt(newUser.email);
            dbUser.profileId = userNewProfile.id;
            const bcrypt = require('bcrypt');
            const saltRounds = 10;
            dbUser.password = await bcrypt.hash(newUser.password, saltRounds);
            dbUser.agencyId = newUser.agencyId;
            dbUser.cooperativeId = newUser.cooperativeId;
            dbUser.centralId = newUser.centralId;
            dbUser.federationId = newUser.federationId;
            dbUser.cnpj = newUser.cnpj;
            dbUser.cpf = this.cryptography.encrypt(newUser.cpf);
            dbUser.serviceUnitNumber = newUser.serviceUnitNumber;
            dbUser.birthDate = newUser.birthDate;
            dbUser.geralRegister = newUser.geralRegister;
            const { id, name, lastName, phone, email } = await this.usersRepository.save(dbUser);
            return {
                id,
                name,
                lastName,
                phone: this.cryptography.decrypt(phone),
                email: this.cryptography.decrypt(email),
            };
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to create user');
        }
    }
    async findByUserLogin(email) {
        const emailCryptography = this.cryptography.encrypt(email);
        const userFound = await this.usersRepository
            .createQueryBuilder('user')
            .where('user.email = :email', { email: emailCryptography })
            .andWhere('user.deleted_at IS NULL')
            .getOne();
        if (!userFound) {
            return null;
        }
        return {
            id: userFound.id,
            name: userFound.name,
            lastName: userFound.lastName,
            phone: this.cryptography.decrypt(userFound.phone),
            email: this.cryptography.decrypt(userFound.email),
            profileKey: 'admin',
        };
    }
    async findByDocuments(cpfList, cnpjList) {
        if ((!cpfList || cpfList.length === 0) &&
            (!cnpjList || cnpjList.length === 0)) {
            return [];
        }
        return this.usersRepository.find({
            where: [{ cpf: (0, typeorm_1.In)(cpfList) }, { cnpj: (0, typeorm_1.In)(cnpjList) }],
        });
    }
    async searchByName(searchUser) {
        const queryBuilder = this.usersRepository.createQueryBuilder('user');
        queryBuilder
            .select(['user.id', 'user.name', 'user.email', 'user.active'])
            .where('user.deleted_at IS NULL');
        if (searchUser.name) {
            queryBuilder.andWhere('user.name LIKE :name', {
                name: `%${searchUser.name}%`,
            });
        }
        const users = await queryBuilder.getMany();
        users.forEach(async (user) => {
            user.email = this.cryptography.decrypt(user.email);
        });
        return users;
    }
    async findById(id) {
        const user = await this.usersRepository.find({
            where: {
                id,
                deletedAt: (0, typeorm_1.IsNull)(),
            },
            relations: {
                profile: true,
            },
        });
        if (!user) {
            this.logger.error(`User with ID ${id} not found`, { status: common_1.HttpStatus.NOT_FOUND }, UsersService_1.name);
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        user[0].phone = this.cryptography.decrypt(user[0].phone);
        user[0].email = this.cryptography.decrypt(user[0].email);
        user[0].cpf = this.cryptography.decrypt(user[0].cpf);
        return user[0];
    }
    async remove(id, token) {
        const user = await this.findById(id);
        if (!user) {
            throw new common_1.NotFoundException(`Usuário com ID ${id} não encontrado.`);
        }
        await this.usersRepository.update(id, {
            deletedAt: new Date(),
            active: false,
        });
    }
    async photoUpload(id, photo) {
        const user = await this.usersRepository.findOneBy({ id });
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        user.photo = photo;
        const { name, lastName, phone, email } = await this.usersRepository.save(user);
        return { id, name, lastName, phone, email };
    }
    async findByIdKeyCloeker(searchUser) {
        const user = await this.usersRepository.findOneBy({
            idUserKeycloak: searchUser.idUserKeycloak,
        });
        if (!user) {
            this.logger.error(`User with keycloak ID ${searchUser.idUserKeycloak} not found`, { status: common_1.HttpStatus.NOT_FOUND }, UsersService_1.name);
            throw new common_1.NotFoundException(`User with ID ${searchUser.idUserKeycloak} not found`);
        }
        user.phone = this.cryptography.decrypt(user.phone);
        user.email = this.cryptography.decrypt(user.email);
        const permissions = await this.profilePermissionsService.findAllByProfileId(user.profileId);
        const profile = await this.profileService.findOne(user.profileId);
        if (!profile) {
            throw new common_1.NotFoundException(`Profile with ID ${user.profileId} not found`);
        }
        const hierarchy = profile.hierarchy;
        const profileName = profile.name;
        return {
            ...user,
            permissions,
            hierarchy,
            profileName,
            profileKey: profile.key,
        };
    }
    async findAll() {
        const queryBuilder = this.usersRepository.createQueryBuilder('user');
        queryBuilder
            .select('user.id', 'id')
            .addSelect('user.name', 'name')
            .addSelect('user.last_name', 'lastName')
            .addSelect('user.email', 'email')
            .addSelect('user.phone', 'phone')
            .addSelect('user.profile_id', 'profileId')
            .addSelect('user.keycloak_id', 'keycloakId')
            .addSelect('user.keycloak_username', 'keycloakUsername')
            .addSelect('user.ldap', 'ldap')
            .addSelect(`(SELECT profile.name FROM profile WHERE profile.id = user.profile_id)`, 'profileName')
            .where('user.deleted_at IS NULL');
        const users = await queryBuilder.execute();
        users.forEach(async (user) => {
            user.email = this.cryptography.decrypt(user.email);
            user.phone = this.cryptography.decrypt(user.phone);
        });
        return users;
    }
    async testeCripto(data) {
        const teste = this.cryptography.encrypt(JSON.stringify(data));
        return teste;
    }
    async findPaginatedUsers(userHierarchy, paginationParams) {
        const { page = 1, limit = 10, filter, agencyId } = paginationParams;
        const queryBuilder = this.usersRepository.createQueryBuilder('user');
        const helper = new queryHelper_1.QueryHelper(queryBuilder);
        helper
            .select('user.id', 'id')
            .addSelect('user.name', 'name')
            .addSelect('user.email', 'email')
            .addSelect('user.phone', 'phone')
            .addSelect('user.profile_id', 'profileId')
            .addSelect('user.keycloak_id', 'keycloakId')
            .addSelect('user.keycloak_username', 'keycloakUsername')
            .addSelect('user.ldap', 'ldap')
            .addSelect('profile.name', 'profileName')
            .whereHierarchy('>', userHierarchy, 'profile_id')
            .orderBy('user.id', 'ASC');
        helper.andWhere('user.deleted_at IS NULL');
        if (filter) {
            const encryptedFilter = this.cryptography.encrypt(filter);
            helper.andWhere('(LOWER(user.name) LIKE :filter OR LOWER(user.email) LIKE :encryptedFilter)', {
                filter: `%${filter.toLowerCase()}%`,
                encryptedFilter: `%${encryptedFilter.toLowerCase()}%`,
            });
        }
        if (agencyId) {
            helper.andWhere('user.agencyId = :agencyId', { agencyId });
        }
        const totalItems = await helper.getQueryBuilder().getCount();
        helper.paginate(page, limit);
        const users = await helper.getRawMany();
        const items = users.map((user) => ({
            ...user,
            email: this.cryptography.decrypt(user.email),
            phone: this.cryptography.decrypt(user.phone),
        }));
        const totalPages = Math.ceil(totalItems / limit);
        return {
            items,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllByAgency(id, filter, cpf) {
        const rules = ['WALLET_MANAGER', 'ASSISTANT'];
        const profiles = await this.profileService.findAll(1);
        const profilesFiltered = profiles.filter((profile) => rules.includes(profile.key));
        const profileIds = profilesFiltered.map((profile) => profile.id);
        const queryBuilder = this.usersRepository.createQueryBuilder('user');
        queryBuilder
            .select('user.id', 'id')
            .addSelect('user.name', 'name')
            .addSelect('user.lastName', 'lastName')
            .addSelect('user.email', 'email')
            .addSelect('user.phone', 'phone')
            .addSelect('user.profileId', 'profileId')
            .addSelect('profile.key', 'profile')
            .addSelect('profile.key', 'profileKey')
            .addSelect('profile.name', 'profileName')
            .innerJoin('profile', 'profile', 'profile.id = user.profileId')
            .where('user.deletedAt IS NULL')
            .andWhere('user.agencyId = :id', { id })
            .andWhere('user.profileId IN (:...profileIds)', { profileIds });
        if (filter) {
            queryBuilder.andWhere('(LOWER(user.name) LIKE :filter OR LOWER(user.lastName) LIKE :filter)', { filter: `%${filter.toLowerCase()}%` });
        }
        if (cpf) {
            const normalizeCPF = (cpf) => {
                return cpf.replace(/\D/g, '');
            };
            const normalizedCpf = normalizeCPF(cpf);
            const encryptedCpf = this.cryptography.encrypt(normalizedCpf);
            queryBuilder.andWhere('user.cpf = :encryptedCpf', { encryptedCpf });
        }
        const users = await queryBuilder.execute();
        users.forEach((user) => {
            user.email = this.cryptography.decrypt(user.email);
            user.phone = this.cryptography.decrypt(user.phone);
        });
        return users;
    }
    async findAllByKeyProfile(data) {
        const profiles = await this.profileService.findAll(1);
        const profilesFiltered = profiles.filter((profile) => data.includes(profile.key));
        const profileIds = profilesFiltered.map((profile) => profile.id);
        const queryBuilder = this.usersRepository.createQueryBuilder('user');
        queryBuilder
            .select([
            'user.id AS id',
            'user.name AS name',
            'user.last_name AS lastName',
            'user.email AS email',
            'user.phone AS phone',
            'user.profile_id AS profileId',
        ])
            .where('user.deleted_at IS NULL')
            .andWhere('user.profile_id IN (:...profileIds)', { profileIds });
        return await queryBuilder.getRawMany();
    }
    async findByKeycloakId(keycloakId) {
        const queryBuilder = this.usersRepository.createQueryBuilder('user');
        queryBuilder
            .select([
            'user.id AS id',
            'user.name AS name',
            'user.last_name AS lastName',
            'user.email AS email',
            'user.phone AS phone',
            'user.profile_id AS profileId',
            'user.id_user_keycloak AS keycloakId',
            'user.agencyId AS agencyId',
            'user.cooperativeId AS cooperativeId',
            'user.centralId AS centralId',
            'profile.id AS "profileId"',
            'user.federationId AS federationId',
            'profile.name AS "profileName"',
            'profile.key AS "profileKey"',
        ])
            .leftJoin('profile', 'profile', 'profile.id = user.profile_id')
            .where('user.deleted_at IS NULL')
            .andWhere('user.id_user_keycloak = :keycloakId', { keycloakId });
        return await queryBuilder.getRawOne();
    }
    async createUserFromBulk(user, token) {
        const MAX_BATCH_SIZE = 500;
        if (user.length > MAX_BATCH_SIZE) {
            return {
                status: 'error',
                message: `Too many users in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: user.length,
            };
        }
        const userCreateOrUpdate = [];
        const errors = [];
        for (const userDto of user) {
            try {
                const missingFields = [];
                if (!userDto.profileKey)
                    missingFields.push('profileKey');
                if (!userDto.cooperative_name)
                    missingFields.push('cooperative_name');
                if (!userDto.agency_code)
                    missingFields.push('agency_code');
                if (!userDto.central_name)
                    missingFields.push('central_name');
                if (!userDto.federation_name)
                    missingFields.push('federation_name');
                if (missingFields.length > 0) {
                    throw new Error(`Missing required fields in JSON: ${missingFields.join(', ')}`);
                }
                const userNewProfile = await this.profileService.findByKey(userDto.profileKey);
                const cooperative = await this.cooperativeService.findOne(userDto.cooperative_name);
                const agencies = await this.agenciesSevice.findOne(userDto.agency_code);
                const central = await this.centralService.findOne(userDto.central_name);
                const federation = await this.federationsService.findOne(userDto.federation_name);
                const missingEntities = [];
                if (!userNewProfile)
                    missingEntities.push('Profile does not exist for the given profileKey');
                if (!cooperative)
                    missingEntities.push('Cooperative not found for the given cooperative_name');
                if (!agencies)
                    missingEntities.push('Agency not found for the given agency_code');
                if (!central)
                    missingEntities.push('Central not found for the given central_name');
                if (!federation)
                    missingEntities.push('Federation not found for the given federation_name');
                if (missingEntities.length > 0) {
                    throw new Error(missingEntities.join('. '));
                }
                const encryptedEmail = this.cryptography.encrypt(userDto.email);
                let existingUser = await this.usersRepository.findOne({
                    where: { email: encryptedEmail },
                });
                if (existingUser) {
                    if (userDto.phone)
                        existingUser.phone = this.cryptography.encrypt(userDto.phone);
                    if (userDto.name)
                        existingUser.name = userDto.name;
                    if (userDto.lastName)
                        existingUser.lastName = userDto.lastName;
                    if (userDto.active !== undefined)
                        existingUser.active = userDto.active;
                    if (userDto.cnpj)
                        existingUser.cnpj = userDto.cnpj;
                    if (userDto.serviceUnitNumber)
                        existingUser.serviceUnitNumber = userDto.serviceUnitNumber;
                    if (userDto.birthDate)
                        existingUser.birthDate = userDto.birthDate;
                    if (userDto.cpf)
                        existingUser.cpf = this.cryptography.encrypt(userDto.cpf);
                    if (userDto.profileKey)
                        existingUser.profileId = userNewProfile.id;
                    if (userDto.agency_code)
                        existingUser.agencyId = agencies.id;
                    if (userDto.central_name)
                        existingUser.centralId = central.id;
                    if (userDto.federation_name)
                        existingUser.federationId = federation.id;
                    if (userDto.cooperative_name)
                        existingUser.cooperativeId = cooperative.id;
                    const updatedUser = await this.usersRepository.save(existingUser);
                    userCreateOrUpdate.push(updatedUser);
                    continue;
                }
                const bcrypt = require('bcrypt');
                const hashedPassword = await bcrypt.hash(userDto.password, 10);
                const userEntity = new user_entity_1.User();
                userEntity.phone = this.cryptography.encrypt(userDto.phone);
                userEntity.name = userDto.name;
                userEntity.lastName = userDto.lastName;
                userEntity.email = encryptedEmail;
                userEntity.password = hashedPassword;
                userEntity.profileId = userNewProfile.id;
                userEntity.active = userDto.active;
                userEntity.photo = userDto.phone;
                userEntity.agencyId = agencies.id;
                userEntity.centralId = central.id;
                userEntity.federationId = federation.id;
                userEntity.cnpj = userDto.cnpj;
                userEntity.cpf = this.cryptography.encrypt(userDto.cpf);
                userEntity.cooperativeId = cooperative.id;
                userEntity.serviceUnitNumber = userDto.serviceUnitNumber;
                userEntity.birthDate = userDto.birthDate;
                const savedUser = await this.usersRepository.save(userEntity);
                userCreateOrUpdate.push(savedUser);
            }
            catch (error) {
                errors.push({
                    user: userDto,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            processedUsers: userCreateOrUpdate,
            errors,
        };
    }
    async findByDocument(document) {
        const normalizedDocument = document.replace(/\D/g, '');
        const encryptedDocument = this.cryptography.encrypt(normalizedDocument);
        const queryBuilder = this.usersRepository.createQueryBuilder('user');
        queryBuilder
            .select(['user', 'profile.hierarchy', 'profile.key', 'profile.name'])
            .innerJoin('user.profile', 'profile')
            .where('user.cpf = :cpf', { cpf: encryptedDocument })
            .orWhere('user.cnpj = :cnpj', { cnpj: encryptedDocument })
            .andWhere('user.deletedAt IS NULL');
        const user = await queryBuilder.getOne();
        if (!user) {
            throw new common_1.NotFoundException(`User with document ${document} not found`);
        }
        return user;
    }
    async findByEmail(email) {
        const emailCryptography = this.cryptography.encrypt(email);
        const userFound = await this.usersRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.profile', 'profile')
            .where('user.email = :email', { email: emailCryptography })
            .andWhere('user.deleted_at IS NULL')
            .getOne();
        if (!userFound) {
            return null;
        }
        userFound.phone = this.cryptography.decrypt(userFound.phone);
        userFound.email = this.cryptography.decrypt(userFound.email);
        if (userFound.cpf) {
            userFound.cpf = this.cryptography.decrypt(userFound.cpf);
        }
        return userFound;
    }
    async findOne(id) {
        try {
            const userId = typeof id === 'string' ? parseInt(id, 10) : id;
            return await this.usersRepository.findOne({
                where: { id: userId },
                relations: {
                    profile: true,
                    cooperative: true,
                    agency: true,
                    central: true,
                },
            });
        }
        catch (error) {
            this.logger.error(`Error finding user by id: ${id}`, error);
            return null;
        }
    }
    async findByKeycloakUsername(username) {
        try {
            const user = await this.usersRepository.findOne({
                where: { keycloakUsername: username },
                relations: {
                    profile: true,
                    cooperative: true,
                    agency: true,
                    central: true,
                    federation: true,
                },
            });
            if (!user) {
                return null;
            }
            user.phone = this.cryptography.decrypt(user.phone);
            user.email = this.cryptography.decrypt(user.email);
            if (user.cpf) {
                user.cpf = this.cryptography.decrypt(user.cpf);
            }
            const permissions = await this.profilePermissionsService.findAllByProfileId(user.profileId);
            const profile = await this.profileService.findOne(user.profileId);
            if (!profile) {
                this.logger.warn(`Profile with ID ${user.profileId} not found for user ${username}`);
            }
            const hierarchy = profile?.hierarchy;
            const profileName = profile?.name;
            const profileKey = profile?.key;
            return {
                ...user,
                permissions,
                hierarchy,
                profileName,
                profileKey,
            };
        }
        catch (error) {
            this.logger.error(`Error finding user by Keycloak username: ${username}`, error);
            return null;
        }
    }
    async findByLdap(ldap) {
        try {
            return await this.usersRepository.findOne({
                where: { ldap: ldap },
                relations: {
                    profile: true,
                    cooperative: true,
                    agency: true,
                    central: true,
                },
            });
        }
        catch (error) {
            this.logger.error(`Error finding user by LDAP: ${ldap}`, error);
            return null;
        }
    }
    async findByLdapOrKeycloakUsername(identifier) {
        try {
            let user = await this.findByLdap(identifier);
            if (!user) {
                user = await this.findByKeycloakUsername(identifier);
            }
            return user;
        }
        catch (error) {
            this.logger.error(`Error finding user by LDAP or Keycloak username: ${identifier}`, error);
            return null;
        }
    }
    async createFromKeycloakUser(keycloakUserData) {
        try {
            const existingUser = await this.findByEmail(keycloakUserData.email);
            if (existingUser) {
                existingUser.keycloakId = keycloakUserData.keycloakId;
                existingUser.keycloakUsername = keycloakUserData.username;
                if (keycloakUserData.sub) {
                    existingUser.ldap = keycloakUserData.sub;
                }
                const profileKey = keycloak_role_mapper_1.KeycloakRoleMapper.mapRolesToProfile(keycloakUserData.roles);
                const profile = await this.profileService.findByKey(profileKey);
                if (profile) {
                    existingUser.profileId = profile.id;
                    this.logger.log(`Perfil do usuário ${keycloakUserData.username} atualizado para ${profileKey}`);
                }
                else {
                    this.logger.warn(`Profile ${profileKey} not found for user ${keycloakUserData.username}`);
                }
                const savedUser = await this.usersRepository.save(existingUser);
                return await this.usersRepository.findOne({
                    where: { id: savedUser.id },
                    relations: {
                        profile: true,
                        cooperative: true,
                        agency: true,
                        central: true,
                        federation: true,
                    },
                });
            }
            const profileKey = keycloak_role_mapper_1.KeycloakRoleMapper.mapRolesToProfile(keycloakUserData.roles);
            const profile = await this.profileService.findByKey(profileKey);
            if (!profile) {
                throw new Error(`Profile ${profileKey} not found`);
            }
            const newUser = new user_entity_1.User();
            newUser.name = keycloakUserData.name;
            newUser.lastName = keycloakUserData.lastName;
            newUser.email = this.cryptography.encrypt(keycloakUserData.email);
            newUser.phone = this.cryptography.encrypt('');
            newUser.cpf = this.cryptography.encrypt('');
            newUser.keycloakId = keycloakUserData.keycloakId;
            newUser.keycloakUsername = keycloakUserData.username;
            if (keycloakUserData.sub) {
                newUser.ldap = keycloakUserData.sub;
            }
            newUser.profileId = profile.id;
            newUser.createdAt = new Date();
            const savedUser = await this.usersRepository.save(newUser);
            return await this.usersRepository.findOne({
                where: { id: savedUser.id },
                relations: {
                    profile: true,
                    cooperative: true,
                    agency: true,
                    central: true,
                    federation: true,
                },
            });
        }
        catch (error) {
            this.logger.error('Error creating user from Keycloak data:', error);
            throw new common_1.BadRequestException('Failed to create user from Keycloak data');
        }
    }
    async updateFromKeycloakUser(userId, keycloakUserData) {
        try {
            const user = await this.findById(userId);
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            user.keycloakId = keycloakUserData.keycloakId;
            user.keycloakUsername = keycloakUserData.username;
            user.name = keycloakUserData.name;
            user.lastName = keycloakUserData.lastName;
            user.email = this.cryptography.encrypt(keycloakUserData.email);
            if (keycloakUserData.sub) {
                user.ldap = keycloakUserData.sub;
            }
            user.updatedAt = new Date();
            return await this.usersRepository.save(user);
        }
        catch (error) {
            this.logger.error('Error updating user from Keycloak data:', error);
            throw new common_1.BadRequestException('Failed to update user from Keycloak data');
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = UsersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(user_entity_1.User)),
    __param(2, (0, common_1.Inject)((0, common_1.forwardRef)(() => profiles_service_1.ProfilesService))),
    __param(4, (0, common_1.Inject)((0, common_1.forwardRef)(() => centrals_service_1.CentralsService))),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        common_1.Logger,
        profiles_service_1.ProfilesService,
        cooperatives_service_1.CooperativesService,
        centrals_service_1.CentralsService,
        agencies_service_1.AgenciesService,
        profile_permissions_service_1.ProfilePermissionsService,
        federations_service_1.FederationsService,
        cryptography_1.Cryptography])
], UsersService);
//# sourceMappingURL=users.service.js.map