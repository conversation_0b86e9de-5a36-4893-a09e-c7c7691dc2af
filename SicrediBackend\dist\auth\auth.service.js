"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const rxjs_1 = require("rxjs");
const axios_1 = require("@nestjs/axios");
const jwt_decode_1 = require("jwt-decode");
const users_service_1 = require("./../modules/users/users.service");
const typeorm_1 = require("typeorm");
const attendance_entity_1 = require("../modules/attendances/entities/attendance.entity");
const date_fns_1 = require("date-fns");
let AuthService = class AuthService {
    constructor(configService, http, usersService, dataSource) {
        this.configService = configService;
        this.http = http;
        this.usersService = usersService;
        this.dataSource = dataSource;
        this.keycloakUrl = this.configService.get('SICREDI_KEYCLOAK_URL') || 'https://auth-em.dev.sicredi.io';
        this.keycloakRealm = this.configService.get('SICREDI_KEYCLOAK_REALM') || 'employee';
        this.keycloakClientId = this.configService.get('SICREDI_KEYCLOAK_CLIENT_ID') || 'sicredi-public';
        this.keycloakScope = this.configService.get('SICREDI_KEYCLOAK_SCOPE') || 'openid application:portal_aplicacoes';
    }
    async signIn(login, password) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/token`, new URLSearchParams({
                grant_type: 'password',
                client_id: this.keycloakClientId,
                username: login,
                password: password,
                scope: this.keycloakScope,
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            }));
            if (status !== 200) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            if (!data.access_token) {
                throw new common_1.UnauthorizedException('Invalid Access Token');
            }
            const decodedToken = (0, jwt_decode_1.jwtDecode)(data.access_token);
            if (!decodedToken?.sub) {
                throw new common_1.UnauthorizedException('Invalid Access Token');
            }
            const userInfo = await this.getUserInfo(data.access_token);
            try {
                let user = await this.usersService.findByKeycloakUsername(decodedToken.preferred_username);
                const keycloakRoles = this.extractRolesFromToken(decodedToken);
                console.log('=== DEBUG KEYCLOAK ROLES ===');
                console.log('Username:', decodedToken.preferred_username);
                console.log('Roles extraídas:', keycloakRoles);
                console.log('Token roles direto:', decodedToken.roles || []);
                console.log('Realm Access Roles:', decodedToken.realm_access?.roles || []);
                console.log('Resource Access Roles:', decodedToken.resource_access || {});
                console.log('===========================');
                if (!user) {
                    user = await this.usersService.createFromKeycloakUser({
                        keycloakId: decodedToken.sub,
                        username: decodedToken.preferred_username,
                        email: userInfo.email,
                        name: userInfo.given_name || userInfo.name,
                        lastName: userInfo.family_name || '',
                        roles: keycloakRoles,
                        sub: decodedToken.sub,
                    });
                }
                let userData = user;
                if (user.profile?.key === 'WALLET_MANAGER' || user.profile?.key === 'ASSISTANT') {
                    const queryRunner = this.dataSource.createQueryRunner();
                    const todayStart = (0, date_fns_1.startOfDay)(new Date());
                    const todayEnd = (0, date_fns_1.endOfDay)(new Date());
                    const attendanceCount = await queryRunner.manager
                        .getRepository(attendance_entity_1.Attendance)
                        .createQueryBuilder('attendance')
                        .where('attendance.attendant_id = :id', { id: user.id })
                        .andWhere('attendance.created_at BETWEEN :start AND :end', {
                        start: todayStart,
                        end: todayEnd,
                    })
                        .getCount();
                    userData = {
                        ...user,
                        hasAttendedToday: attendanceCount > 0,
                    };
                    await queryRunner.release();
                }
                return {
                    access_token: data.access_token,
                    refresh_token: data.refresh_token,
                    token_type: data.token_type,
                    expires_in: data.expires_in,
                    id_token: data.id_token,
                    scope: data.scope,
                    user: userData,
                    keycloak_user_info: userInfo,
                };
            }
            catch (error) {
                console.log('Erro ao buscar/criar usuário local:', error);
                return {
                    access_token: data.access_token,
                    refresh_token: data.refresh_token,
                    token_type: data.token_type,
                    expires_in: data.expires_in,
                    id_token: data.id_token,
                    scope: data.scope,
                    user: {
                        id: decodedToken.sub,
                        email: userInfo.email,
                        name: userInfo.name,
                        username: decodedToken.preferred_username,
                        roles: decodedToken.roles,
                    },
                    keycloak_user_info: userInfo,
                };
            }
        }
        catch (error) {
            console.error('Erro no login Keycloak:', error?.response?.data || error.message);
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
    }
    async refreshAccessToken(refreshToken) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/token`, new URLSearchParams({
                grant_type: 'refresh_token',
                client_id: this.keycloakClientId,
                refresh_token: refreshToken,
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            }));
            if (status !== 200) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            return {
                access_token: data.access_token,
                refresh_token: data.refresh_token,
                token_type: data.token_type,
                expires_in: data.expires_in,
                id_token: data.id_token,
                scope: data.scope,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async validateToken(token) {
        try {
            const userInfo = await this.getUserInfo(token);
            return userInfo;
        }
        catch (e) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    async findByToken(token) {
        try {
            const decodedToken = (0, jwt_decode_1.jwtDecode)(token);
            if (!decodedToken?.preferred_username) {
                throw new common_1.UnauthorizedException('Invalid Access Token');
            }
            const user = await this.usersService.findByKeycloakUsername(decodedToken.preferred_username);
            if (user) {
                return user;
            }
            const userInfo = await this.getUserInfo(token);
            const keycloakRoles = this.extractRolesFromToken(decodedToken);
            return {
                id: decodedToken.sub,
                email: userInfo.email,
                name: userInfo.name,
                username: decodedToken.preferred_username,
                roles: keycloakRoles,
                keycloak_user_info: userInfo,
            };
        }
        catch (e) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    async getUserInfo(accessToken) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.get(`${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/userinfo`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            }));
            if (status !== 200) {
                throw new common_1.UnauthorizedException('Failed to get user info');
            }
            return data;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Failed to get user info');
        }
    }
    extractRolesFromToken(decodedToken) {
        let keycloakRoles = [];
        if (decodedToken.roles && Array.isArray(decodedToken.roles)) {
            keycloakRoles = [...keycloakRoles, ...decodedToken.roles];
        }
        if (decodedToken.realm_access?.roles && Array.isArray(decodedToken.realm_access.roles)) {
            keycloakRoles = [...keycloakRoles, ...decodedToken.realm_access.roles];
        }
        if (decodedToken.resource_access) {
            Object.keys(decodedToken.resource_access).forEach(clientId => {
                const clientRoles = decodedToken.resource_access[clientId]?.roles;
                if (clientRoles && Array.isArray(clientRoles)) {
                    keycloakRoles = [...keycloakRoles, ...clientRoles];
                }
            });
        }
        return [...new Set(keycloakRoles)];
    }
    async logout(refreshToken) {
        try {
            const { status } = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/auth/realms/${this.keycloakRealm}/protocol/openid-connect/logout`, new URLSearchParams({
                client_id: this.keycloakClientId,
                refresh_token: refreshToken,
            }), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            }));
            return status === 204 || status === 200;
        }
        catch (error) {
            console.error('Erro no logout:', error);
            return false;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService,
        users_service_1.UsersService,
        typeorm_1.DataSource])
], AuthService);
//# sourceMappingURL=auth.service.js.map