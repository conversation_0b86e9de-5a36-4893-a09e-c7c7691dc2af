{"level":"info","message":{"data":{"body":{"payload":{"encrypted":"DoVjZiv3EsghB1WsHDeSc0dUwa+NVwK2kvFmKALTLlrdwR0IONqSkw3X+cdp9xsfpg==","iv":"10O8ybBaaE+ztrmC6XC8sw==","salt":"K1ViczFjdWNNQ293V1RxNnRYSmlxUT09"}},"params":{},"query":{}},"from":"::1","madeBy":null,"method":"POST","route":"/api/v1/auth/login","timestamp":"2025-07-31T14:00:06.310Z"}}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/auth/me","timestamp":"2025-07-31T14:00:16.124Z"}}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/auth/me","timestamp":"2025-07-31T14:00:19.594Z"}}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/segments","timestamp":"2025-07-31T14:00:24.784Z"}}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/segments","timestamp":"2025-07-31T14:00:25.534Z"}}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/segments","timestamp":"2025-07-31T14:00:33.193Z"}}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/segments","timestamp":"2025-07-31T14:00:33.703Z"}}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"level":"error","message":"Error finding user by id: 2","stack":[{"code":"ERR_INVALID_ARG_VALUE"}]}
{"context":"AllExceptionsFilter","level":"error","message":"Erro capturado: {\"message\":\"Access denied: user not found\",\"error\":\"Forbidden\",\"statusCode\":403}","stack":[null]}
{"context":"NestFactory","level":"info","message":"Starting Nest application..."}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CommonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"KeycloakModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalProductWalletModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CooperativesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FederationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SegmentsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilePermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuditLogsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserAgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"EventsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateAgencyAccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceStatusModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceHistoryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SchedulesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PropensityProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FiltersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountTypeModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateDetailsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CardsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatePhoneModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CentralsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletRangeValuesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsEffectiveModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendancesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletsModule dependencies initialized"}
{"context":"RoutesResolver","level":"info","message":"AppController {/}:"}
{"context":"RoutesResolver","level":"info","message":"GoalProductWalletController {/api/v1/goal-product-wallet}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal-product-wallet/wallet-manager, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, GET} route"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/search-by-name, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/find-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/user-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/paginated-users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CooperativesController {/api/v1/cooperatives}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-central-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-federation-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"CentralsController {/api/v1/centrals}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/get-by-federation/:federationId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"FederationsController {/api/v1/federations}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AgenciesController {/api/v1/agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-codes, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletRangeValuesController {/api/v1/wallet-range-values}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-number-by-agency-and-segment, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/find-by-segment/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/to-update/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SegmentsController {/api/v1/segments}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletsController {/api/v1/wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-update-wallet/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-create-wallet, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/search-number, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-all-users-and-wallet-range-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-central-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-federation-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged-and-filtered, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-associate-and-agency, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatesController {/api/v1/associates}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/remove-wallet/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/account-wallet-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserWalletsController {/user-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilePermissionsController {/api/v1/profile-permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilesController {/api/v1/profiles}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"GoalController {/api/v1/goal}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"AuditLogsController {/api/v1/audit-logs}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"PermissionsController {/api/v1/permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserAgenciesController {/api/v1/user-agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProductsController {/api/v1/products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"EventsController {/api/v1/events}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateAgencyAccountsController {/api/v1/associate-agency-accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceStatusController {/api/v1/attendance-status}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendancesController {/api/v1/attendances}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant-paginated/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceHistoryController {/api/v1/attendance-history}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SchedulesController {/api/v1/schedules}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/filter, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/schedulesByAgencyId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsController {/api/v1/attendance-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsEffectiveController {/api/v1/attendance-products-effective}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyController {/api/v1/strategy}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/propensity, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyProductsController {/api/v1/strategy-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"PropensityProductsController {/api/v1/propensity-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletSummaryController {/api/v1/wallet-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"FiltersController {/api/v1/wallet-summary-filters}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/status, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceSummaryController {/api/v1/attendance-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountsController {/api/v1/accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountTypeController {/api/v1/account-types}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AccountWalletsController {/api/v1/account-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateDetailsController {/api/v1/associate-details}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CardsController {/api/v1/cards}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/account/:accountId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/next-manager-schedule, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/weekly-manager-summaries, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/daily-manager-portfolio-turnover, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/viewed/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatePhoneController {/api/v1/associate-phone}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create-many, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/associate/:id, GET} route"}
{"context":"NestFactory","level":"info","message":"Starting Nest application..."}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CommonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"KeycloakModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalProductWalletModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CooperativesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FederationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SegmentsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilePermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuditLogsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserAgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"EventsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateAgencyAccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceStatusModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceHistoryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SchedulesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PropensityProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FiltersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountTypeModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateDetailsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CardsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatePhoneModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CentralsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletRangeValuesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsEffectiveModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendancesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletsModule dependencies initialized"}
{"context":"RoutesResolver","level":"info","message":"AppController {/}:"}
{"context":"RoutesResolver","level":"info","message":"GoalProductWalletController {/api/v1/goal-product-wallet}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal-product-wallet/wallet-manager, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, GET} route"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/search-by-name, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/find-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/user-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/paginated-users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CooperativesController {/api/v1/cooperatives}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-central-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-federation-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"CentralsController {/api/v1/centrals}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/get-by-federation/:federationId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"FederationsController {/api/v1/federations}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AgenciesController {/api/v1/agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-codes, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletRangeValuesController {/api/v1/wallet-range-values}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-number-by-agency-and-segment, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/find-by-segment/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/to-update/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SegmentsController {/api/v1/segments}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletsController {/api/v1/wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-update-wallet/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-create-wallet, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/search-number, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-all-users-and-wallet-range-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-central-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-federation-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged-and-filtered, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-associate-and-agency, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatesController {/api/v1/associates}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/remove-wallet/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/account-wallet-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserWalletsController {/user-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilePermissionsController {/api/v1/profile-permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilesController {/api/v1/profiles}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"GoalController {/api/v1/goal}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"AuditLogsController {/api/v1/audit-logs}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"PermissionsController {/api/v1/permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserAgenciesController {/api/v1/user-agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProductsController {/api/v1/products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"EventsController {/api/v1/events}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateAgencyAccountsController {/api/v1/associate-agency-accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceStatusController {/api/v1/attendance-status}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendancesController {/api/v1/attendances}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant-paginated/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceHistoryController {/api/v1/attendance-history}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SchedulesController {/api/v1/schedules}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/filter, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/schedulesByAgencyId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsController {/api/v1/attendance-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsEffectiveController {/api/v1/attendance-products-effective}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyController {/api/v1/strategy}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/propensity, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyProductsController {/api/v1/strategy-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"PropensityProductsController {/api/v1/propensity-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletSummaryController {/api/v1/wallet-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"FiltersController {/api/v1/wallet-summary-filters}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/status, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceSummaryController {/api/v1/attendance-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountsController {/api/v1/accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountTypeController {/api/v1/account-types}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AccountWalletsController {/api/v1/account-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateDetailsController {/api/v1/associate-details}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CardsController {/api/v1/cards}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/account/:accountId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/next-manager-schedule, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/weekly-manager-summaries, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/daily-manager-portfolio-turnover, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/viewed/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatePhoneController {/api/v1/associate-phone}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create-many, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/associate/:id, GET} route"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started"}
{"context":"NestFactory","level":"info","message":"Starting Nest application..."}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CommonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"KeycloakModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalProductWalletModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CooperativesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FederationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SegmentsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilePermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuditLogsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserAgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"EventsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateAgencyAccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceStatusModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceHistoryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SchedulesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PropensityProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FiltersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountTypeModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateDetailsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CardsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatePhoneModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CentralsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletRangeValuesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsEffectiveModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendancesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletsModule dependencies initialized"}
{"context":"RoutesResolver","level":"info","message":"AppController {/}:"}
{"context":"RoutesResolver","level":"info","message":"GoalProductWalletController {/api/v1/goal-product-wallet}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal-product-wallet/wallet-manager, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/test-role-mapping, POST} route"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/search-by-name, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/find-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/user-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/paginated-users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CooperativesController {/api/v1/cooperatives}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-central-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-federation-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"CentralsController {/api/v1/centrals}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/get-by-federation/:federationId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"FederationsController {/api/v1/federations}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AgenciesController {/api/v1/agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-codes, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletRangeValuesController {/api/v1/wallet-range-values}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-number-by-agency-and-segment, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/find-by-segment/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/to-update/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SegmentsController {/api/v1/segments}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletsController {/api/v1/wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-update-wallet/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-create-wallet, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/search-number, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-all-users-and-wallet-range-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-central-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-federation-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged-and-filtered, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-associate-and-agency, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatesController {/api/v1/associates}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/remove-wallet/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/account-wallet-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserWalletsController {/user-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilePermissionsController {/api/v1/profile-permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilesController {/api/v1/profiles}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"GoalController {/api/v1/goal}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"AuditLogsController {/api/v1/audit-logs}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"PermissionsController {/api/v1/permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserAgenciesController {/api/v1/user-agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProductsController {/api/v1/products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"EventsController {/api/v1/events}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateAgencyAccountsController {/api/v1/associate-agency-accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceStatusController {/api/v1/attendance-status}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendancesController {/api/v1/attendances}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant-paginated/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceHistoryController {/api/v1/attendance-history}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SchedulesController {/api/v1/schedules}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/filter, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/schedulesByAgencyId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsController {/api/v1/attendance-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsEffectiveController {/api/v1/attendance-products-effective}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyController {/api/v1/strategy}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/propensity, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyProductsController {/api/v1/strategy-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"PropensityProductsController {/api/v1/propensity-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletSummaryController {/api/v1/wallet-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"FiltersController {/api/v1/wallet-summary-filters}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/status, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceSummaryController {/api/v1/attendance-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountsController {/api/v1/accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountTypeController {/api/v1/account-types}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AccountWalletsController {/api/v1/account-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateDetailsController {/api/v1/associate-details}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CardsController {/api/v1/cards}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/account/:accountId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/next-manager-schedule, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/weekly-manager-summaries, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/daily-manager-portfolio-turnover, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/viewed/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatePhoneController {/api/v1/associate-phone}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create-many, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/associate/:id, GET} route"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started"}
{"context":"NestFactory","level":"info","message":"Starting Nest application..."}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CommonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"HttpModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WinstonModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"KeycloakModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalProductWalletModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CooperativesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FederationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SegmentsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilePermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProfilesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"GoalModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuditLogsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PermissionsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserAgenciesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"ProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"EventsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateAgencyAccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceStatusModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceHistoryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"SchedulesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"StrategyProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"PropensityProductsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"FiltersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceSummaryModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountTypeModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociateDetailsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CardsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"NotificationsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AssociatePhoneModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"CentralsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UsersModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletRangeValuesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendanceProductsEffectiveModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"UserWalletsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AttendancesModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"AccountsModule dependencies initialized"}
{"context":"InstanceLoader","level":"info","message":"WalletsModule dependencies initialized"}
{"context":"RoutesResolver","level":"info","message":"AppController {/}:"}
{"context":"RoutesResolver","level":"info","message":"GoalProductWalletController {/api/v1/goal-product-wallet}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal-product-wallet/wallet-manager, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/login, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/test-role-mapping, POST} route"}
{"context":"RoutesResolver","level":"info","message":"UsersController {/api/v1/users}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/search-by-name, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/find-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/user-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/paginated-users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CooperativesController {/api/v1/cooperatives}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-central-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/get-by-federation-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cooperatives/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"CentralsController {/api/v1/centrals}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/get-by-federation/:federationId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/centrals/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"FederationsController {/api/v1/federations}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/federations/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AgenciesController {/api/v1/agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/get-codes, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/agencies/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletRangeValuesController {/api/v1/wallet-range-values}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-number-by-agency-and-segment, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/find-by-segment/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/get-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/to-update/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-range-values/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SegmentsController {/api/v1/segments}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/segments/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletsController {/api/v1/wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-update-wallet/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/to-create-wallet, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/search-number, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-cooperative/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-user-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-agency-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-all-users-and-wallet-range-by-agency/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-central-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-by-federation-id-paged/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/paged-and-filtered, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/find-associate-and-agency, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallets/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatesController {/api/v1/associates}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/paged, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/remove-wallet/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/account-wallet-by-id/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associates/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserWalletsController {/user-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/user-wallets/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilePermissionsController {/api/v1/profile-permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profile-permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProfilesController {/api/v1/profiles}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/profiles/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"GoalController {/api/v1/goal}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/goal/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"AuditLogsController {/api/v1/audit-logs}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/audit-logs/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"PermissionsController {/api/v1/permissions}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/permissions/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"UserAgenciesController {/api/v1/user-agencies}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/user-agencies/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"ProductsController {/api/v1/products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/products/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"EventsController {/api/v1/events}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/events/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateAgencyAccountsController {/api/v1/associate-agency-accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-agency-accounts/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceStatusController {/api/v1/attendance-status}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-status/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendancesController {/api/v1/attendances}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendances/by-attendant-paginated/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceHistoryController {/api/v1/attendance-history}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-history/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"SchedulesController {/api/v1/schedules}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/filter, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/schedulesByAgencyId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/schedules/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsController {/api/v1/attendance-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceProductsEffectiveController {/api/v1/attendance-products-effective}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/attendance-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-products-effective/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyController {/api/v1/strategy}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/paginated, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/propensity, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"StrategyProductsController {/api/v1/strategy-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/strategy-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"PropensityProductsController {/api/v1/propensity-products}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/propensity-products/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"WalletSummaryController {/api/v1/wallet-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"FiltersController {/api/v1/wallet-summary-filters}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/segments, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/agencies, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/users, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/wallets, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/wallet-summary-filters/status, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AttendanceSummaryController {/api/v1/attendance-summary}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/attendance-summary, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountsController {/api/v1/accounts}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/delete/bulk, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/accounts/:id, GET} route"}
{"context":"RoutesResolver","level":"info","message":"AccountTypeController {/api/v1/account-types}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-types/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AccountWalletsController {/api/v1/account-wallets}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/bulk, PUT} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/account-wallets/delete/bulk, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociateDetailsController {/api/v1/associate-details}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/by-associate/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/:id, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-details/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"CardsController {/api/v1/cards}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/:id, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/account/:accountId, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/cards/bulk, PUT} route"}
{"context":"RoutesResolver","level":"info","message":"NotificationsController {/api/v1/notifications}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/next-manager-schedule, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/weekly-manager-summaries, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/daily-manager-portfolio-turnover, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, GET} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/viewed/:id, PATCH} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications, DELETE} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/notifications/:id, DELETE} route"}
{"context":"RoutesResolver","level":"info","message":"AssociatePhoneController {/api/v1/associate-phone}:"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/create-many, POST} route"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/associate-phone/associate/:id, GET} route"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started"}
{"level":"info","message":{"data":{"body":{"payload":{"encrypted":"97uD7ZgmZOpTRuEhHyL/zyBWKE346Ql8rCFI4AWp+oRjd9wBbLFEITm9jP3XLq8Wsg==","iv":"Z1eLQoajYxUrg0FKqpF8mg==","salt":"K1ViczFjdWNNQ293V1RxNnRYSmlxUT09"}},"params":{},"query":{}},"from":"::1","madeBy":null,"method":"POST","route":"/api/v1/auth/login","timestamp":"2025-07-31T14:22:35.064Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/notifications","timestamp":"2025-07-31T14:22:39.211Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/wallet-summary-filters/segments","timestamp":"2025-07-31T14:22:39.642Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{"userProfile":"ASSISTANT"}},"from":"::1","method":"GET","route":"/api/v1/wallet-summary-filters/wallets","timestamp":"2025-07-31T14:22:39.891Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/attendance-summary","timestamp":"2025-07-31T14:22:40.031Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/wallet-summary-filters/wallets","timestamp":"2025-07-31T14:22:40.182Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/wallet-summary-filters/status","timestamp":"2025-07-31T14:22:40.281Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/segments","timestamp":"2025-07-31T14:22:41.233Z"}}
{"level":"info","message":{"data":{"body":{},"params":{},"query":{}},"from":"::1","method":"GET","route":"/api/v1/segments","timestamp":"2025-07-31T14:22:41.962Z"}}
