[{"merged": "com.sicrediApp.app-debug-61:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.sicrediApp.app-debug-61:/drawable_rn_edit_text_material.xml.flat", "source": "com.sicrediApp.app-main-63:/drawable/rn_edit_text_material.xml"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.sicrediApp.app-debug-61:/xml_network_security_config.xml.flat", "source": "com.sicrediApp.app-main-63:/xml/network_security_config.xml"}, {"merged": "com.sicrediApp.app-debug-61:/drawable_ic_launcher_background.xml.flat", "source": "com.sicrediApp.app-main-63:/drawable/ic_launcher_background.xml"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.sicrediApp.app-debug-61:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.sicrediApp.app-main-63:/mipmap-hdpi/ic_launcher_round.webp"}]