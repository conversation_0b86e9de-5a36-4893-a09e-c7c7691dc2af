{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1207,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1278,1350,1420,1498,1567,1688"}, "to": {"startLines": "45,46,49,50,51,61,62,111,112,116,117,120,124,127,129,134,135,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4183,4283,4554,4652,4752,5627,5706,9685,9778,10124,10208,10466,10794,11024,11166,11571,11649,11799", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "4278,4365,4647,4747,4834,5701,5807,9773,9868,10203,10291,10546,10865,11091,11231,11644,11713,11915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "33,47,57,59,60,76,77,114,115,118,121,122,123,125,126,128,130,131,133,136,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2967,4370,5311,5467,5539,6792,6872,9953,10043,10296,10551,10637,10714,10870,10949,11096,11236,11305,11496,11718,11920", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3032,4449,5377,5534,5622,6867,6951,10038,10119,10379,10632,10709,10789,10944,11019,11161,11300,11390,11566,11794,12002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "48,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4454,5000,5098,5208", "endColumns": "99,97,109,102", "endOffsets": "4549,5093,5203,5306"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "35,36,37,38,39,40,41,132", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3114,3212,3314,3413,3515,3624,3731,11395", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3207,3309,3408,3510,3619,3726,3856,11491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,300,399,539,622,688,783,868,930,1018,1087,1150,1223,1286,1340,1461,1518,1580,1634,1711,1848,1933,2015,2120,2201,2282,2373,2440,2506,2579,2659,2750,2825,2902,2971,3048,3136,3225,3318,3411,3485,3565,3659,3710,3776,3860,3948,4010,4074,4137,4252,4362,4468,4577", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "218,295,394,534,617,683,778,863,925,1013,1082,1145,1218,1281,1335,1456,1513,1575,1629,1706,1843,1928,2010,2115,2196,2277,2368,2435,2501,2574,2654,2745,2820,2897,2966,3043,3131,3220,3313,3406,3480,3560,3654,3705,3771,3855,3943,4005,4069,4132,4247,4357,4463,4572,4652"}, "to": {"startLines": "2,34,42,43,44,52,53,58,63,64,65,66,67,68,69,70,71,72,73,74,75,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3037,3861,3960,4100,4839,4905,5382,5812,5874,5962,6031,6094,6167,6230,6284,6405,6462,6524,6578,6655,6956,7041,7123,7228,7309,7390,7481,7548,7614,7687,7767,7858,7933,8010,8079,8156,8244,8333,8426,8519,8593,8673,8767,8818,8884,8968,9056,9118,9182,9245,9360,9470,9576,9873", "endLines": "5,34,42,43,44,52,53,58,63,64,65,66,67,68,69,70,71,72,73,74,75,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113", "endColumns": "12,76,98,139,82,65,94,84,61,87,68,62,72,62,53,120,56,61,53,76,136,84,81,104,80,80,90,66,65,72,79,90,74,76,68,76,87,88,92,92,73,79,93,50,65,83,87,61,63,62,114,109,105,108,79", "endOffsets": "268,3109,3955,4095,4178,4900,4995,5462,5869,5957,6026,6089,6162,6225,6279,6400,6457,6519,6573,6650,6787,7036,7118,7223,7304,7385,7476,7543,7609,7682,7762,7853,7928,8005,8074,8151,8239,8328,8421,8514,8588,8668,8762,8813,8879,8963,9051,9113,9177,9240,9355,9465,9571,9680,9948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "139,140", "startColumns": "4,4", "startOffsets": "12007,12105", "endColumns": "97,98", "endOffsets": "12100,12199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "273,378,481,590,674,779,898,976,1051,1143,1237,1330,1424,1525,1619,1716,1811,1903,1995,2076,2182,2289,2387,2491,2597,2704,2867,10384", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "373,476,585,669,774,893,971,1046,1138,1232,1325,1419,1520,1614,1711,1806,1898,1990,2071,2177,2284,2382,2486,2592,2699,2862,2962,10461"}}]}]}