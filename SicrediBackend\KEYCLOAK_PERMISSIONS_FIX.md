# Correção de Permissões e Profile Keys - Keycloak

## ✅ Status: IMPLEMENTADO COM SUCESSO

As correções para o problema de permissões e profile keys após a migração para Keycloak externo foram implementadas com sucesso.

## 🐛 Problemas Identificados

### 1. **Extração Incorreta de Roles do Token Keycloak**
- **Problema:** O código estava tentando acessar `decodedToken.roles` diretamente
- **Causa:** No Keycloak, as roles podem estar em diferentes locais do token:
  - `decodedToken.roles` (direto)
  - `decodedToken.realm_access.roles` (roles do realm)
  - `decodedToken.resource_access[client_id].roles` (roles do client)

### 2. **Método `findByKeycloakUsername` Não Retornava Permissões**
- **Problema:** O método retornava apenas dados básicos do usuário
- **Causa:** Não estava buscando permissões do perfil como outros métodos faziam

### 3. **JwtAuthGuard Não Extraía Roles Corretamente**
- **Problema:** Guard usava `decodedToken.roles` diretamente
- **Causa:** Mesma lógica incorreta de extração de roles

### 4. **Usuários Existentes Não Tinham Perfil Atualizado**
- **Problema:** Quando usuário existente fazia login, perfil não era atualizado baseado nas roles do Keycloak
- **Causa:** Método `createFromKeycloakUser` não atualizava o perfil de usuários existentes

## 🔧 Correções Implementadas

### 1. **AuthService** (`src/auth/auth.service.ts`)

#### ✅ Método `extractRolesFromToken()` Adicionado
```typescript
private extractRolesFromToken(decodedToken: any): string[] {
  let keycloakRoles: string[] = [];
  
  // Extrair de decodedToken.roles
  if (decodedToken.roles && Array.isArray(decodedToken.roles)) {
    keycloakRoles = [...keycloakRoles, ...decodedToken.roles];
  }
  
  // Extrair de realm_access.roles
  if (decodedToken.realm_access?.roles && Array.isArray(decodedToken.realm_access.roles)) {
    keycloakRoles = [...keycloakRoles, ...decodedToken.realm_access.roles];
  }
  
  // Extrair de resource_access[client_id].roles
  if (decodedToken.resource_access) {
    Object.keys(decodedToken.resource_access).forEach(clientId => {
      const clientRoles = decodedToken.resource_access[clientId]?.roles;
      if (clientRoles && Array.isArray(clientRoles)) {
        keycloakRoles = [...keycloakRoles, ...clientRoles];
      }
    });
  }
  
  // Remover duplicatas
  return [...new Set(keycloakRoles)];
}
```

#### ✅ Métodos `signIn()` e `findByToken()` Atualizados
- Agora usam `extractRolesFromToken()` para extrair roles corretamente
- Logs de debug melhorados para mostrar roles extraídas

### 2. **UsersService** (`src/modules/users/users.service.ts`)

#### ✅ Método `findByKeycloakUsername()` Melhorado
```typescript
async findByKeycloakUsername(username: string): Promise<any | null> {
  // Busca usuário com todas as relações
  const user = await this.usersRepository.findOne({
    where: { keycloakUsername: username },
    relations: {
      profile: true,
      cooperative: true,
      agency: true,
      central: true,
      federation: true,
    },
  });

  if (!user) return null;

  // Descriptografar dados sensíveis
  user.phone = this.cryptography.decrypt(user.phone);
  user.email = this.cryptography.decrypt(user.email);
  if (user.cpf) {
    user.cpf = this.cryptography.decrypt(user.cpf);
  }

  // Buscar permissões do perfil
  const permissions = await this.profilePermissionsService.findAllByProfileId(user.profileId);
  
  // Buscar dados do perfil
  const profile = await this.profileService.findOne(user.profileId);
  
  return {
    ...user,
    permissions,
    hierarchy: profile?.hierarchy,
    profileName: profile?.name,
    profileKey: profile?.key,
  };
}
```

#### ✅ Método `createFromKeycloakUser()` Melhorado
- Agora atualiza o perfil de usuários existentes baseado nas roles do Keycloak
- Mapeia roles para perfil usando `KeycloakRoleMapper.mapRolesToProfile()`
- Retorna usuário completo com todas as relações

### 3. **JwtAuthGuard** (`src/guards/jwt-auth.guard.ts`)

#### ✅ Método `extractRolesFromToken()` Adicionado
- Mesma lógica do AuthService para consistência

#### ✅ Método `canActivate()` Melhorado
```typescript
// Extrair roles corretamente
const keycloakRoles = this.extractRolesFromToken(decodedToken);

// Adicionar mais informações ao request.user
request.user = {
  // ... dados existentes
  keycloakRoles: keycloakRoles, // Roles extraídas corretamente
  permissions: user.permissions || [],
  hierarchy: user.hierarchy,
  profileName: user.profileName,
  profileKey: user.profileKey,
};
```

## 🧪 Testes Realizados

### ✅ Teste de Extração de Roles
- Testado com diferentes estruturas de token Keycloak
- Verificado que roles são extraídas de todas as fontes possíveis
- Confirmado que duplicatas são removidas
- Validado mapeamento correto para perfis do sistema

### ✅ Cenários Testados
1. **Token com roles diretas:** `decodedToken.roles`
2. **Token com realm_access:** `decodedToken.realm_access.roles`
3. **Token com resource_access:** `decodedToken.resource_access[client].roles`
4. **Token com múltiplas fontes:** Combinação de todas as fontes

## 📋 Resultados Esperados

### ✅ Após as Correções
1. **Usuários terão permissões corretas** baseadas no perfil mapeado das roles do Keycloak
2. **Telas mostrarão dados e botões** conforme as permissões do usuário
3. **Profile keys funcionarão corretamente** para controle de acesso
4. **Usuários existentes** terão perfil atualizado automaticamente no próximo login
5. **Novos usuários** serão criados com perfil correto baseado nas roles

### ✅ Logs de Debug
- Logs detalhados mostram roles extraídas do token
- Mapeamento de roles para perfil é logado
- Erros são capturados e logados adequadamente

## 🚀 Próximos Passos

1. **Testar em ambiente de desenvolvimento** com usuários reais do Keycloak
2. **Verificar logs** para confirmar extração correta de roles
3. **Validar permissões** em diferentes telas da aplicação
4. **Monitorar** criação/atualização de usuários

## 📝 Notas Importantes

- As correções são **retrocompatíveis** e não quebram funcionalidades existentes
- **Usuários existentes** terão dados atualizados automaticamente no próximo login
- **Performance** não é impactada significativamente pelas mudanças
- **Logs de debug** podem ser removidos após validação em produção
