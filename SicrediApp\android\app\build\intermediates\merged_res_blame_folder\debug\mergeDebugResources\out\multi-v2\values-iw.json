{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "37,38,39,40,41,42,43,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3171,3265,3367,3464,3561,3662,3762,11171", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3260,3362,3459,3556,3657,3757,3863,11267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,319,396,489,602,682,747,835,905,968,1060,1120,1179,1242,1303,1362,1464,1521,1580,1638,1706,1817,1898,1980,2082,2153,2226,2314,2381,2447,2520,2596,2682,2752,2827,2909,2977,3062,3132,3222,3313,3387,3460,3549,3600,3667,3749,3834,3896,3960,4023,4117,4212,4302,4398", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "314,391,484,597,677,742,830,900,963,1055,1115,1174,1237,1298,1357,1459,1516,1575,1633,1701,1812,1893,1975,2077,2148,2221,2309,2376,2442,2515,2591,2677,2747,2822,2904,2972,3057,3127,3217,3308,3382,3455,3544,3595,3662,3744,3829,3891,3955,4018,4112,4207,4297,4393,4468"}, "to": {"startLines": "2,36,44,45,46,54,55,60,65,67,68,69,70,71,72,73,74,75,76,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3094,3868,3961,4074,4767,4832,5298,5680,5811,5903,5963,6022,6085,6146,6205,6307,6364,6423,6481,6549,6884,6965,7047,7149,7220,7293,7381,7448,7514,7587,7663,7749,7819,7894,7976,8044,8129,8199,8289,8380,8454,8527,8616,8667,8734,8816,8901,8963,9027,9090,9184,9279,9369,9630", "endLines": "7,36,44,45,46,54,55,60,65,67,68,69,70,71,72,73,74,75,76,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,117", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "364,3166,3956,4069,4149,4827,4915,5363,5738,5898,5958,6017,6080,6141,6200,6302,6359,6418,6476,6544,6655,6960,7042,7144,7215,7288,7376,7443,7509,7582,7658,7744,7814,7889,7971,8039,8124,8194,8284,8375,8449,8522,8611,8662,8729,8811,8896,8958,9022,9085,9179,9274,9364,9460,9700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "369,474,574,682,766,868,984,1063,1141,1232,1326,1420,1514,1614,1707,1802,1895,1986,2078,2159,2264,2367,2465,2570,2672,2774,2928,10175", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "469,569,677,761,863,979,1058,1136,1227,1321,1415,1509,1609,1702,1797,1890,1981,2073,2154,2259,2362,2460,2565,2667,2769,2923,3020,10252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "50,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4405,4920,5020,5126", "endColumns": "90,99,105,101", "endOffsets": "4491,5015,5121,5223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,87", "endOffsets": "137,225"}, "to": {"startLines": "145,146", "startColumns": "4,4", "startOffsets": "11830,11917", "endColumns": "86,87", "endOffsets": "11912,12000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,341,422,490,558,636,714,796,875,946,1024,1104,1177,1257,1335,1410,1482,1554,1641,1712,1791,1860", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,197,267,336,417,485,553,631,709,791,870,941,1019,1099,1172,1252,1330,1405,1477,1549,1636,1707,1786,1855,1930"}, "to": {"startLines": "35,49,59,61,62,66,79,80,81,118,119,122,123,126,127,128,130,131,133,135,136,138,141,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3025,4327,5228,5368,5437,5743,6660,6728,6806,9705,9787,10026,10097,10326,10406,10479,10633,10711,10864,11012,11084,11272,11488,11686,11755", "endColumns": "68,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3089,4400,5293,5432,5513,5806,6723,6801,6879,9782,9861,10092,10170,10401,10474,10554,10706,10781,10931,11079,11166,11338,11562,11750,11825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,954,1036,1105,1179,1257,1333,1407,1478", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,949,1031,1100,1174,1252,1328,1402,1473,1592"}, "to": {"startLines": "47,48,51,52,53,63,64,115,116,120,121,125,129,132,134,139,140,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4154,4243,4496,4589,4684,5518,5595,9465,9551,9866,9944,10257,10559,10786,10936,11343,11417,11567", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,73,77,75,73,70,118", "endOffsets": "4238,4322,4584,4679,4762,5590,5675,9546,9625,9939,10021,10321,10628,10859,11007,11412,11483,11681"}}]}]}