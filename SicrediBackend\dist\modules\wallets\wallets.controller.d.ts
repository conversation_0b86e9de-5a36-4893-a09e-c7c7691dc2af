import { WalletsService } from './wallets.service';
import { CreateWalletDto } from './dto/create-wallet.dto';
import { UpdateWalletDto } from './dto/update-wallet.dto';
import { SearchWalletDto } from './dto/search-wallet.dto';
import { Wallet } from './entities/wallet.entity';
import { FindAllUsersAndWalletRangeResponseDto } from './dto/find-all-users-and-wallet-range-response.dto';
import { WalletDto } from './dto/wallet.dto';
import { ResponseAccountDto } from '../accounts/dto/response-account.dto';
export declare class WalletsController {
    private readonly walletsService;
    constructor(walletsService: WalletsService);
    create(createWalletDto: CreateWalletDto): Promise<CreateWalletDto>;
    findAll(): Promise<CreateWalletDto[]>;
    findAllToUpdateWallet(id: string, req: any): Promise<{
        wallet: Wallet;
        associates: import("../associates/entities/associate.entity").Associate[];
        agency: import("../agencies/dto/create-agency.dto").CreateAgencyDto;
        usersToWallet: import("../users/dto/user.dto").UserDto[];
    }>;
    findAllToCreateWallet(user: any): Promise<import("./dto/to-create-wallet.dto").ToCreateWalletDto>;
    update(id: string, updateWalletDto: UpdateWalletDto): Promise<UpdateWalletDto>;
    remove(id: string): Promise<string>;
    findNumber(searchWalletDto: SearchWalletDto): Promise<{
        number: string;
        numberOld: string;
    }>;
    findAllWalletsByCooperativeIdPaged(id: string, page?: number, pageSize?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: import("./dto/paginated-wallet-number-associate.dto").PaginatedWalletNumberAssociateDto[];
    }>;
    findAllWalletsByCooperativeId(id: string, searchWalletDto: {
        page: number;
        limit: number;
    }): Promise<CreateWalletDto[]>;
    findAllWalletsByAgencyId(id: string, searchWalletDto: {
        page: number;
        limit: number;
    }): Promise<CreateWalletDto[]>;
    findAllWalletsByUserId(id: string): Promise<CreateWalletDto[]>;
    findAllPaged(user: any, page?: number, pageSize?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: import("./dto/paginated-wallet-number-associate.dto").PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByUserIdPaged(user: any, id: string, page?: number, pageSize?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: import("./dto/paginated-wallet-number-associate.dto").PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByAgencyIdPaged(id: string, page?: number, pageSize?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: import("./dto/paginated-wallet-number-associate.dto").PaginatedWalletNumberAssociateDto[];
    }>;
    findAllUsersAndwalletRangesByAgencyId(id: string): Promise<FindAllUsersAndWalletRangeResponseDto>;
    findAllByCentralIdPaged(id: string, page?: number, pageSize?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: import("./dto/paginated-wallet-number-associate.dto").PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByFederationIdPaged(id: string, page?: number, pageSize?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: import("./dto/paginated-wallet-number-associate.dto").PaginatedWalletNumberAssociateDto[];
    }>;
    findAllPagedAndFiltered(user: any, page?: number, pageSize?: number, segmentId?: number, walletRangeId?: number): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: import("./dto/paginated-wallet-number-associate.dto").PaginatedWalletNumberAssociateDto[];
    }>;
    findAllByAssociateAndAgency(user: any, associateId?: number): Promise<CreateWalletDto[]>;
    findOne(id: string): Promise<{
        wallet: Wallet;
        associates: import("../associates/entities/associate.entity").Associate[];
        agency: import("../agencies/dto/create-agency.dto").CreateAgencyDto;
    }>;
    createBulk(walletDto: WalletDto[]): Promise<any>;
    deleteBulk(body: {
        walletsData: {
            wallet_number: string;
            agency_code: string;
        }[];
    }): Promise<any>;
    findAccountsByAgencyId(id: number): Promise<ResponseAccountDto[]>;
}
