import { Repository } from 'typeorm';
import { UserWallet } from './entities/user-wallets.entity';
import { UsersService } from '../users/users.service';
export declare class UserWalletsService {
    private readonly userWalletRepository;
    private readonly usersService;
    constructor(userWalletRepository: Repository<UserWallet>, usersService: UsersService);
    create(userWalletDto: Partial<UserWallet>): Promise<UserWallet>;
    findAll(): Promise<UserWallet[]>;
    remove(id: number): Promise<void>;
    findOneByUserAndWallet(idUser: number, idWallet: number): Promise<UserWallet>;
}
