import { AttendanceProductsEffectiveService } from './attendance-products-effective.service';
import { CreateAttendanceProductEffectiveDto } from './dto/create-attendance-products-effective.dto';
import { UpdateAttendanceProductEffectiveDto } from './dto/update-attendance-products-effective.dto';
import { AttendanceProductEffectiveDto } from './dto/attendance-product-effective.dto';
export declare class AttendanceProductsEffectiveController {
    private readonly service;
    constructor(service: AttendanceProductsEffectiveService);
    create(dto: CreateAttendanceProductEffectiveDto | CreateAttendanceProductEffectiveDto[]): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    } | {
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }[]>;
    findAllProducts(): Promise<any>;
    findAllAttendanceProducts(): Promise<any>;
    findAll(): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }[]>;
    findOne(id: string): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }>;
    update(id: string, dto: UpdateAttendanceProductEffectiveDto): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
    }>;
    createBulk(attendanceProductEffectiveDto: AttendanceProductEffectiveDto[]): Promise<any>;
}
