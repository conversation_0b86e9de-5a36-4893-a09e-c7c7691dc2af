import { WalletRangeValuesService } from './wallet-range-values.service';
import { CreateWalletRangeValueDto } from './dto/create-wallet-range-value.dto';
import { UpdateWalletRangeValueDto } from './dto/update-wallet-range-value.dto';
import { WalletRangeValuesDto } from './dto/wallet-range-values.dto';
export declare class WalletRangeValuesController {
    private readonly walletRangeValuesService;
    constructor(walletRangeValuesService: WalletRangeValuesService);
    create(createWalletRangeValueDto: CreateWalletRangeValueDto): Promise<{
        id: number;
        name: string;
    }>;
    findNumbersByAgencyId(agencyId: string, segmentId: string): Promise<string[]>;
    findAll(): Promise<CreateWalletRangeValueDto[]>;
    findOne(id: string): Promise<import("./entities/wallet-range-value.entity").WalletRangeValue>;
    update(id: string, updateWalletRangeValueDto: UpdateWalletRangeValueDto): Promise<UpdateWalletRangeValueDto>;
    remove(id: string): Promise<void>;
    findBySegment(id: string): Promise<CreateWalletRangeValueDto[]>;
    findByAgency(id: string): Promise<CreateWalletRangeValueDto[]>;
    findByCooperative(id: string): Promise<CreateWalletRangeValueDto[]>;
    toUpdateWalletRange(id: string): Promise<import("./dto/to-update-wallet-range.dto").ToUpdateWalletRangeDto>;
    createBulk(walletRangeDto: WalletRangeValuesDto[]): Promise<any>;
    deleteBulk(walletRangeCodes: string[]): Promise<any>;
}
