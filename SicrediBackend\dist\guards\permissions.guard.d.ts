import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionsService } from 'src/modules/permissions/permissions.service';
import { ProfilePermissionsService } from 'src/modules/profile-permissions/profile-permissions.service';
import { UsersService } from 'src/modules/users/users.service';
import { ProfilesService } from 'src/modules/profiles/profiles.service';
export declare class PermissionsGuard implements CanActivate {
    private readonly reflector;
    private readonly permissionsService;
    private readonly profilePermissionsService;
    private readonly usersService;
    private readonly profilesService;
    constructor(reflector: Reflector, permissionsService: PermissionsService, profilePermissionsService: ProfilePermissionsService, usersService: UsersService, profilesService: ProfilesService);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private mapHttpMethodToOperation;
}
