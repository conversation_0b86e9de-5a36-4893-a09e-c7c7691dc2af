{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,396,504,617,705,811,926,1006,1083,1174,1267,1362,1456,1556,1649,1744,1838,1929,2020,2104,2213,2323,2424,2534,2652,2760,2923,9680", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "391,499,612,700,806,921,1001,1078,1169,1262,1357,1451,1551,1644,1739,1833,1924,2015,2099,2208,2318,2419,2529,2647,2755,2918,3020,9760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,91", "endOffsets": "136,228"}, "to": {"startLines": "118,119", "startColumns": "4,4", "startOffsets": "10443,10529", "endColumns": "85,91", "endOffsets": "10524,10616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,235,321,426,562,647,712,811,879,938,1027,1094,1157,1232,1300,1354,1474,1532,1594,1648,1723,1865,1955,2040,2155,2239,2322,2418,2485,2551,2625,2703,2794,2868,2947,3020,3092,3196,3269,3368,3468,3542,3617,3724,3776,3843,3934,4028,4090,4154,4217,4336,4438,4547,4650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "230,316,421,557,642,707,806,874,933,1022,1089,1152,1227,1295,1349,1469,1527,1589,1643,1718,1860,1950,2035,2150,2234,2317,2413,2480,2546,2620,2698,2789,2863,2942,3015,3087,3191,3264,3363,3463,3537,3612,3719,3771,3838,3929,4023,4085,4149,4212,4331,4433,4542,4645,4730"}, "to": {"startLines": "2,33,41,42,43,50,51,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3845,3950,4086,4748,4813,5233,5475,5534,5623,5690,5753,5828,5896,5950,6070,6128,6190,6244,6319,6461,6551,6636,6751,6835,6918,7014,7081,7147,7221,7299,7390,7464,7543,7616,7688,7792,7865,7964,8064,8138,8213,8320,8372,8439,8530,8624,8686,8750,8813,8932,9034,9143,9422", "endLines": "5,33,41,42,43,50,51,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,106", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "280,3106,3945,4081,4166,4808,4907,5296,5529,5618,5685,5748,5823,5891,5945,6065,6123,6185,6239,6314,6456,6546,6631,6746,6830,6913,7009,7076,7142,7216,7294,7385,7459,7538,7611,7683,7787,7860,7959,8059,8133,8208,8315,8367,8434,8525,8619,8681,8745,8808,8927,9029,9138,9241,9502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "34,35,36,37,38,39,40,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3111,3208,3310,3411,3508,3615,3723,10071", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3203,3305,3406,3503,3610,3718,3840,10167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,1016,1102,1173,1256,1333,1408,1486,1552", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,1011,1097,1168,1251,1328,1403,1481,1547,1674"}, "to": {"startLines": "44,45,47,48,49,56,57,104,105,107,108,110,111,112,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4171,4270,4459,4556,4658,5301,5383,9246,9338,9507,9594,9765,9836,9919,9996,10172,10250,10316", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "4265,4351,4551,4653,4743,5378,5470,9333,9417,9589,9675,9831,9914,9991,10066,10245,10311,10438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "46,52,53,54", "startColumns": "4,4,4,4", "startOffsets": "4356,4912,5017,5128", "endColumns": "102,104,110,104", "endOffsets": "4454,5012,5123,5228"}}]}]}