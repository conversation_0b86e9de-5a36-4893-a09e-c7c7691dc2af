{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,211,282,352,435,502,569,648,727,815,908,976,1062,1147,1223,1306,1388,1463,1541,1615,1701,1773,1852,1928", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "120,206,277,347,430,497,564,643,722,810,903,971,1057,1142,1218,1301,1383,1458,1536,1610,1696,1768,1847,1923,2009"}, "to": {"startLines": "29,39,47,48,49,52,53,54,55,58,59,62,63,66,67,68,70,71,73,75,76,78,81,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2843,3829,4629,4700,4770,5033,5100,5167,5246,5503,5591,5859,5927,6175,6260,6336,6495,6577,6731,6884,6958,7145,7360,7552,7628", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "2908,3910,4695,4765,4848,5095,5162,5241,5320,5586,5679,5922,6008,6255,6331,6414,6572,6647,6804,6953,7039,7212,7434,7623,7709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,6013", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,6094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,1011,1101,1177,1253,1332,1407,1483,1550", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,1006,1096,1172,1248,1327,1402,1478,1545,1658"}, "to": {"startLines": "37,38,41,42,43,50,51,56,57,60,61,65,69,72,74,79,80,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3648,3743,4030,4127,4226,4853,4936,5325,5416,5684,5769,6099,6419,6652,6809,7217,7293,7439", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,75,78,74,75,66,112", "endOffsets": "3738,3824,4122,4221,4307,4931,5028,5411,5498,5764,5854,6170,6490,6726,6879,7288,7355,7547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "30,31,32,33,34,35,36,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2913,3010,3112,3211,3311,3418,3528,7044", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3005,3107,3206,3306,3413,3523,3643,7140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "40,44,45,46", "startColumns": "4,4,4,4", "startOffsets": "3915,4312,4411,4523", "endColumns": "114,98,111,105", "endOffsets": "4025,4406,4518,4624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "7714,7797", "endColumns": "82,84", "endOffsets": "7792,7877"}}]}]}