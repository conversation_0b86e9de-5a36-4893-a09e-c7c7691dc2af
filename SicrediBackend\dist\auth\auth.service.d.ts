import { ConfigService } from '@nestjs/config';
import { AuthResponseDto } from './dto/auth.dto';
import { HttpService } from '@nestjs/axios';
import { UsersService } from './../modules/users/users.service';
import { DataSource } from 'typeorm';
export declare class AuthService {
    private readonly configService;
    private readonly http;
    private readonly usersService;
    private readonly dataSource;
    private readonly keycloakUrl;
    private readonly keycloakRealm;
    private readonly keycloakClientId;
    private readonly keycloakScope;
    constructor(configService: ConfigService, http: HttpService, usersService: UsersService, dataSource: DataSource);
    signIn(login: string, password: string): Promise<AuthResponseDto>;
    refreshAccessToken(refreshToken: string): Promise<any>;
    validateToken(token: string): Promise<any>;
    findByToken(token: string): Promise<any>;
    private getUserInfo;
    private extractRolesFromToken;
    logout(refreshToken: string): Promise<boolean>;
}
