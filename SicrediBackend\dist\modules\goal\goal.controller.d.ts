import { CreateGoalDto } from './dto/create-goal.dto';
import { GoalService } from './goal.service';
import { PaginatedGoalsDto } from './dto/paginated-goal.dto';
import { UpdateGoalDto } from './dto/update-strategy.dto';
import { GoalDto } from './dto/goal.dto';
export declare class GoalController {
    private readonly goalService;
    constructor(goalService: GoalService);
    findPaginatedGoals(user: any, paginationParams: PaginatedGoalsDto): Promise<{
        items: CreateGoalDto[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
    }>;
    findOne(id: string): Promise<CreateGoalDto>;
    create(createGoalDto: CreateGoalDto): Promise<CreateGoalDto>;
    update(id: string, updateGoalDto: UpdateGoalDto): Promise<CreateGoalDto>;
    remove(id: string): Promise<void>;
    createBulk(goalDto: GoalDto[]): Promise<any>;
}
