import { Repository } from 'typeorm';
import { Accounts } from './entities/account.entity';
import { CreateAccountDto } from './dto/create-account.dto';
import { AgenciesService } from '../agencies/agencies.service';
import { AssociatesService } from '../associates/associates.service';
import { AccountTypeService } from '../account-type/account-types.service';
import { Attendance } from '../attendances/entities/attendance.entity';
import { AccountWallets } from '../account-wallets/entities/account-wallets.entity';
import { Cryptography } from 'src/common/functions/cryptography';
export declare class AccountsService {
    private readonly accountRepository;
    private readonly agenciesService;
    private readonly accountWalletsRepository;
    private readonly attendanceRepository;
    private readonly associatesService;
    private readonly accountTypesService;
    private readonly cryptography;
    constructor(accountRepository: Repository<Accounts>, agenciesService: AgenciesService, accountWalletsRepository: Repository<AccountWallets>, attendanceRepository: Repository<Attendance>, associatesService: AssociatesService, accountTypesService: AccountTypeService, cryptography: Cryptography);
    createAccountsFromBulk(accountDto: CreateAccountDto[]): Promise<any>;
    private processAccountBatch;
    deleteAccountsFromBulk(accountCodes: string[]): Promise<any>;
    findOne(identifier: number | string): Promise<CreateAccountDto>;
    findByCodes(codes: string[]): Promise<Accounts[]>;
}
