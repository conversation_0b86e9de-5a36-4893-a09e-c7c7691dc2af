"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersModule = void 0;
const common_1 = require("@nestjs/common");
const users_controller_1 = require("./users.controller");
const users_service_1 = require("./users.service");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("./entities/user.entity");
const keycloak_service_1 = require("../keycloak/keycloak.service");
const profiles_service_1 = require("../profiles/profiles.service");
const axios_1 = require("@nestjs/axios");
const cryptography_1 = require("../../common/functions/cryptography");
const profile_permissions_module_1 = require("../profile-permissions/profile-permissions.module");
const profile_permissions_service_1 = require("../profile-permissions/profile-permissions.service");
const profile_permission_entity_1 = require("../profile-permissions/entities/profile-permission.entity");
const profiles_module_1 = require("../profiles/profiles.module");
const cooperatives_module_1 = require("../cooperatives/cooperatives.module");
const profile_entity_1 = require("../profiles/entities/profile.entity");
const agencies_module_1 = require("../agencies/agencies.module");
const centrals_module_1 = require("../centrals/centrals.module");
const federations_module_1 = require("../federations/federations.module");
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User, profile_permission_entity_1.ProfilePermission, profile_entity_1.Profile]),
            (0, common_1.forwardRef)(() => cooperatives_module_1.CooperativesModule),
            (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule),
            (0, common_1.forwardRef)(() => centrals_module_1.CentralsModule),
            (0, common_1.forwardRef)(() => federations_module_1.FederationsModule),
            axios_1.HttpModule,
            profile_permissions_module_1.ProfilePermissionsModule,
            profiles_module_1.ProfilesModule,
        ],
        controllers: [users_controller_1.UsersController],
        providers: [
            users_service_1.UsersService,
            common_1.Logger,
            keycloak_service_1.KeycloakService,
            profiles_service_1.ProfilesService,
            cryptography_1.Cryptography,
            profile_permissions_service_1.ProfilePermissionsService
        ],
        exports: [users_service_1.UsersService, common_1.Logger],
    })
], UsersModule);
//# sourceMappingURL=users.module.js.map