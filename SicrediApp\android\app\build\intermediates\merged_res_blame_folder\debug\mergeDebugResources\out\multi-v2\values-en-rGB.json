{"logs": [{"outputFile": "com.sicrediApp.app-mergeDebugResources-59:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e4e6dc062f6524f7d84007153e911f3b\\transformed\\foundation-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "128,129", "startColumns": "4,4", "startOffsets": "10752,10838", "endColumns": "85,84", "endOffsets": "10833,10918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\46b2df6d1b0f30613db0bc1444ee909f\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "34,35,36,37,38,39,40,122", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3011,3107,3209,3308,3407,3511,3614,10239", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3102,3204,3303,3402,3506,3609,3725,10335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3382a94a0207bdfac82d33566dd045dc\\transformed\\browser-1.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "47,53,54,55", "startColumns": "4,4,4,4", "startOffsets": "4272,4805,4902,5011", "endColumns": "97,96,108,98", "endOffsets": "4365,4897,5006,5105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6137fb7e3e79fdf321dc94d8330a8f54\\transformed\\material-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,402,517,596,661,751,818,877,967,1031,1094,1163,1227,1281,1393,1451,1513,1567,1639,1761,1848,1929,2039,2116,2197,2288,2355,2421,2491,2568,2655,2726,2803,2872,2941,3032,3104,3193,3282,3356,3428,3514,3564,3630,3710,3794,3856,3920,3983,4083,4180,4272,4371", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "222,299,397,512,591,656,746,813,872,962,1026,1089,1158,1222,1276,1388,1446,1508,1562,1634,1756,1843,1924,2034,2111,2192,2283,2350,2416,2486,2563,2650,2721,2798,2867,2936,3027,3099,3188,3277,3351,3423,3509,3559,3625,3705,3789,3851,3915,3978,4078,4175,4267,4366,4444"}, "to": {"startLines": "2,33,41,42,43,51,52,56,60,61,62,63,64,65,66,67,68,69,70,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2934,3730,3828,3943,4650,4715,5110,5431,5490,5580,5644,5707,5776,5840,5894,6006,6064,6126,6180,6252,6524,6611,6692,6802,6879,6960,7051,7118,7184,7254,7331,7418,7489,7566,7635,7704,7795,7867,7956,8045,8119,8191,8277,8327,8393,8473,8557,8619,8683,8746,8846,8943,9035,9307", "endLines": "5,33,41,42,43,51,52,56,60,61,62,63,64,65,66,67,68,69,70,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,110", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "272,3006,3823,3938,4017,4710,4800,5172,5485,5575,5639,5702,5771,5835,5889,6001,6059,6121,6175,6247,6369,6606,6687,6797,6874,6955,7046,7113,7179,7249,7326,7413,7484,7561,7630,7699,7790,7862,7951,8040,8114,8186,8272,8322,8388,8468,8552,8614,8678,8741,8841,8938,9030,9129,9380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ecdb51c6d8d0115905fab916a7c30fc\\transformed\\react-android-0.76.6-debug\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,288,364,446,526,604,684,758", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "126,209,283,359,441,521,599,679,753,827"}, "to": {"startLines": "46,57,73,74,111,112,115,118,125,127", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4196,5177,6374,6448,9385,9467,9707,9941,10483,10678", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "4267,5255,6443,6519,9462,9542,9780,10016,10552,10747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\238d1a432f173e68336f316376cb6637\\transformed\\ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1613"}, "to": {"startLines": "44,45,48,49,50,58,59,108,109,113,114,117,119,120,121,123,124,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4022,4114,4370,4464,4563,5260,5342,9134,9223,9547,9625,9868,10021,10097,10169,10340,10417,10557", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "4109,4191,4459,4558,4645,5337,5426,9218,9302,9620,9702,9936,10092,10164,10234,10412,10478,10673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1ffd477de46ec477edb993919b2f1d94\\transformed\\appcompat-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,381,481,589,673,773,888,966,1041,1132,1225,1320,1414,1514,1607,1702,1796,1887,1978,2060,2163,2266,2365,2470,2574,2678,2834,9785", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "376,476,584,668,768,883,961,1036,1127,1220,1315,1409,1509,1602,1697,1791,1882,1973,2055,2158,2261,2360,2465,2569,2673,2829,2929,9863"}}]}]}