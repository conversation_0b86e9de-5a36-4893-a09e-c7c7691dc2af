import { Repository, DataSource } from 'typeorm';
import { Cryptography } from 'src/common/functions/cryptography';
import { AttendanceProductEffective } from './entities/attendance-products-effective.entity';
import { CreateAttendanceProductEffectiveDto } from './dto/create-attendance-products-effective.dto';
import { UpdateAttendanceProductEffectiveDto } from './dto/update-attendance-products-effective.dto';
import { Product } from '../products/entities/product.entity';
import { AttendanceProductEffectiveDto } from './dto/attendance-product-effective.dto';
import { Accounts } from '../accounts/entities/account.entity';
import { UsersService } from '../users/users.service';
import { AssociatesService } from '../associates/associates.service';
export declare class AttendanceProductsEffectiveService {
    private readonly repo;
    private readonly accountsRepository;
    private readonly usersService;
    private readonly associatesService;
    private readonly productsRepository;
    private readonly cryptography;
    private readonly dataSource;
    constructor(repo: Repository<AttendanceProductEffective>, accountsRepository: Repository<Accounts>, usersService: UsersService, associatesService: AssociatesService, productsRepository: Repository<Product>, cryptography: Cryptography, dataSource: DataSource);
    create(dto: CreateAttendanceProductEffectiveDto): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }>;
    createBatch(dtos: CreateAttendanceProductEffectiveDto[]): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }[]>;
    findAll(): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }[]>;
    findAllProducts(): Promise<any>;
    findAllAttendanceProducts(): Promise<any>;
    findOne(id: number): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }>;
    update(id: number, dto: UpdateAttendanceProductEffectiveDto): Promise<{
        id: number;
        accountId: number;
        productId: number;
        attendantId: number;
        associateId: number;
        valueOrQuantity: import("../attendance-products/entities/attendance-product.entity").valueOrQuantityEnum;
        quantity: number;
        productValue: string;
    }>;
    remove(id: number): Promise<{
        success: boolean;
    }>;
    private toDto;
    createProductEffectiveFromBulk(attendanceProductEffectiveDto: AttendanceProductEffectiveDto[]): Promise<any>;
}
