import { AccountWalletsService } from "./account-wallets.service";
import { CreateAccountWalletsDto } from "./dto/create-account-wallets.dto";
export declare class AccountWalletsController {
    private readonly accountWalletsService;
    constructor(accountWalletsService: AccountWalletsService);
    createBulk(accountWalletsDto: CreateAccountWalletsDto[]): Promise<any>;
    deleteBulk(body: {
        accountWallets: {
            account_code: string;
            wallet_number: string;
            agency_code: string;
        }[];
    }): Promise<any>;
}
