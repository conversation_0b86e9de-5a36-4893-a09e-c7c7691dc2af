import { CreateAccountDto } from './dto/create-account.dto';
import { AccountsService } from './accounts.service';
export declare class AccountsController {
    private readonly accountsService;
    constructor(accountsService: AccountsService);
    createBulk(accountDto: CreateAccountDto[]): Promise<any>;
    deleteBulk(body: {
        accountCodes: string[];
    }): Promise<any>;
    findOne(id: number): Promise<CreateAccountDto>;
}
